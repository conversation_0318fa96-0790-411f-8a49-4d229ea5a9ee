import { Settings, Template, Stats, Contact, Group } from '../types';

// Chaves do localStorage
export const STORAGE_KEYS = {
  SETTINGS: 'whatsappSenderSettings',
  TEMPLATES: 'whatsappSenderTemplates',
  STATS: 'whatsappSenderStats',
  CONTACTS: 'whatsappSenderContacts',
  GROUPS: 'whatsappSenderGroups',
  CAMPAIGN_PROGRESS: 'whatsappSenderCampaignProgress',
  ALL_TAGS: 'whatsappSenderAllTags',
} as const;

// Funções genéricas para localStorage
export const loadFromStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const saved = localStorage.getItem(key);
    return saved ? JSON.parse(saved) : defaultValue;
  } catch (error) {
    console.error(`Failed to load ${key}`, error);
    return defaultValue;
  }
};

export const saveToStorage = <T>(key: string, value: T): void => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Failed to save ${key}`, error);
  }
};

export const clearFromStorage = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Failed to clear ${key}`, error);
  }
};

// Funções específicas para cada tipo de dados
export const settingsStorage = {
  load: (): Settings => loadFromStorage(STORAGE_KEYS.SETTINGS, {
    baseUrl: '',
    apiKey: '',
    minDelay: '2',
    maxDelay: '5',
    aiProvider: 'gemini' as const,
    aiApiKey: '',
    systemPrompt: 'Você é um especialista em marketing para WhatsApp. Crie mensagens curtas, claras e persuasivas, usando formatação como negrito e itálico para destacar pontos importantes. A mensagem deve ser amigável e terminar com uma chamada para ação clara.'
  }),
  save: (settings: Settings) => saveToStorage(STORAGE_KEYS.SETTINGS, settings),
};

export const templatesStorage = {
  load: (): Template[] => loadFromStorage(STORAGE_KEYS.TEMPLATES, []),
  save: (templates: Template[]) => saveToStorage(STORAGE_KEYS.TEMPLATES, templates),
};

export const statsStorage = {
  load: (): Stats => loadFromStorage(STORAGE_KEYS.STATS, {
    total: 0,
    success: 0,
    errors: 0,
    lastRun: null
  }),
  save: (stats: Stats) => saveToStorage(STORAGE_KEYS.STATS, stats),
};

export const contactsStorage = {
  load: (): Contact[] => loadFromStorage(STORAGE_KEYS.CONTACTS, []),
  save: (contacts: Contact[]) => saveToStorage(STORAGE_KEYS.CONTACTS, contacts),
};

export const groupsStorage = {
  load: (): Group[] => loadFromStorage(STORAGE_KEYS.GROUPS, []),
  save: (groups: Group[]) => saveToStorage(STORAGE_KEYS.GROUPS, groups),
};

export const tagsStorage = {
  load: (): string[] => loadFromStorage(STORAGE_KEYS.ALL_TAGS, []),
  save: (tags: string[]) => saveToStorage(STORAGE_KEYS.ALL_TAGS, tags),
};

export const campaignProgressStorage = {
  load: () => loadFromStorage(STORAGE_KEYS.CAMPAIGN_PROGRESS, null),
  save: (progress: any) => saveToStorage(STORAGE_KEYS.CAMPAIGN_PROGRESS, progress),
  clear: () => clearFromStorage(STORAGE_KEYS.CAMPAIGN_PROGRESS),
};
