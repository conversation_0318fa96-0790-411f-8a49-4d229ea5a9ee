import React, { memo, useMemo, useCallback } from 'react';
import { useVirtualScroll, useDebounce } from '../../hooks/usePerformance';

interface VirtualizedListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  keyExtractor: (item: T, index: number) => string | number;
  className?: string;
  emptyState?: React.ReactNode;
  loading?: boolean;
  loadingComponent?: React.ReactNode;
}

function VirtualizedListComponent<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  keyExtractor,
  className = '',
  emptyState,
  loading = false,
  loadingComponent,
}: VirtualizedListProps<T>) {
  const { visibleItems, totalHeight, setScrollTop } = useVirtualScroll(
    items,
    itemHeight,
    containerHeight
  );

  const debouncedSetScrollTop = useDebounce(setScrollTop, 16); // ~60fps

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    debouncedSetScrollTop(e.currentTarget.scrollTop);
  }, [debouncedSetScrollTop]);

  const memoizedItems = useMemo(() => {
    return visibleItems.map(({ item, index, top }) => (
      <div
        key={keyExtractor(item, index)}
        style={{
          position: 'absolute',
          top,
          left: 0,
          right: 0,
          height: itemHeight,
        }}
      >
        {renderItem(item, index)}
      </div>
    ));
  }, [visibleItems, renderItem, keyExtractor, itemHeight]);

  if (loading) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height: containerHeight }}>
        {loadingComponent || (
          <div className="flex items-center gap-2 text-gray-500">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span>Carregando...</span>
          </div>
        )}
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height: containerHeight }}>
        {emptyState || (
          <div className="text-center text-gray-500">
            <p>Nenhum item encontrado</p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        {memoizedItems}
      </div>
    </div>
  );
}

export const VirtualizedList = memo(VirtualizedListComponent) as <T>(
  props: VirtualizedListProps<T>
) => JSX.Element;
