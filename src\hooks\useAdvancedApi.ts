import { useCallback, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAppStore } from '@/stores/appStore';
import type { Instance, Contact, Group } from '@/types';

interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

interface ApiResponse<T = any> {
  data?: T;
  error?: ApiError;
  success: boolean;
}

// Enhanced API hook with automatic retry and error handling
export const useAdvancedApi = () => {
  const settings = useAppStore((state) => state.settings);
  const abortControllerRef = useRef<AbortController | null>(null);

  const makeRequest = useCallback(async <T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> => {
    try {
      // Cancel previous request if still pending
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      abortControllerRef.current = new AbortController();
      
      const url = `${settings.baseUrl.replace(/\/$/, '')}/${endpoint.replace(/^\//, '')}`;
      
      const response = await fetch(url, {
        ...options,
        signal: abortControllerRef.current.signal,
        headers: {
          'Content-Type': 'application/json',
          'apikey': settings.apiKey,
          ...options.headers,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || `HTTP ${response.status}`);
      }

      return { data, success: true };
    } catch (error: any) {
      if (error.name === 'AbortError') {
        return { success: false, error: { message: 'Request cancelled' } };
      }
      
      return {
        success: false,
        error: {
          message: error.message || 'Unknown error occurred',
          status: error.status,
          code: error.code,
        },
      };
    }
  }, [settings.baseUrl, settings.apiKey]);

  const cancelRequest = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  return { makeRequest, cancelRequest };
};

// Hook for fetching instances with caching
export const useInstancesQuery = () => {
  const { makeRequest } = useAdvancedApi();
  const queryClient = useQueryClient();
  const setInstances = useAppStore((state) => state.setInstances);

  const {
    data: instances = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['instances'],
    queryFn: async () => {
      const response = await makeRequest<Instance[]>('/instance/fetchInstances');
      if (response.success && response.data) {
        // Handle different response formats
        let instancesData = response.data;
        if (Array.isArray(response.data) && response.data.length > 0 && response.data[0].instances) {
          instancesData = response.data[0].instances;
        }
        setInstances(instancesData);
        return instancesData;
      }
      throw new Error(response.error?.message || 'Failed to fetch instances');
    },
    staleTime: 30000, // 30 seconds
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  const createInstanceMutation = useMutation({
    mutationFn: async (instanceName: string) => {
      const response = await makeRequest('/instance/create', {
        method: 'POST',
        body: JSON.stringify({ instanceName, qrcode: true }),
      });
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to create instance');
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['instances'] });
    },
  });

  const getQRCodeMutation = useMutation({
    mutationFn: async (instanceName: string) => {
      const response = await makeRequest(`/instance/connect/${instanceName}`);
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to get QR code');
      }
      return response.data;
    },
  });

  const checkStatusMutation = useMutation({
    mutationFn: async (instanceName: string) => {
      const response = await makeRequest(`/instance/connectionState/${instanceName}`);
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to check status');
      }
      return response.data;
    },
  });

  return {
    instances,
    isLoading,
    error,
    refetch,
    createInstance: createInstanceMutation.mutate,
    getQRCode: getQRCodeMutation.mutate,
    checkStatus: checkStatusMutation.mutate,
    isCreatingInstance: createInstanceMutation.isPending,
    isGettingQRCode: getQRCodeMutation.isPending,
    isCheckingStatus: checkStatusMutation.isPending,
  };
};

// Hook for syncing contacts
export const useContactSync = () => {
  const { makeRequest } = useAdvancedApi();
  const setContacts = useAppStore((state) => state.setContacts);
  const contacts = useAppStore((state) => state.contacts);

  const syncContactsMutation = useMutation({
    mutationFn: async (instanceName: string) => {
      const response = await makeRequest(`/chat/findContacts/${instanceName}`, {
        method: 'POST',
      });
      
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to sync contacts');
      }

      const apiContacts = response.data;
      if (!Array.isArray(apiContacts)) {
        throw new Error('Invalid contacts response format');
      }

      // Process and merge contacts
      const existingPhones = new Set(contacts.map(c => c.phone));
      const newContacts: Contact[] = [];

      apiContacts.forEach((contact: any) => {
        if (!contact.remoteJid) return;
        
        const phone = contact.remoteJid.split('@')[0];
        if (!phone || existingPhones.has(phone)) return;

        newContacts.push({
          id: contact.id || `${phone}-${Date.now()}`,
          name: contact.pushName || 'Sem nome',
          phone,
          tags: [],
          customFields: {},
          createdAt: new Date(),
          updatedAt: new Date(),
          isActive: true,
        });
      });

      if (newContacts.length > 0) {
        setContacts([...contacts, ...newContacts]);
      }

      return { newContacts: newContacts.length, total: apiContacts.length };
    },
  });

  return {
    syncContacts: syncContactsMutation.mutate,
    isSyncing: syncContactsMutation.isPending,
    syncError: syncContactsMutation.error,
    syncResult: syncContactsMutation.data,
  };
};

// Hook for syncing groups
export const useGroupSync = () => {
  const { makeRequest } = useAdvancedApi();
  const setGroups = useAppStore((state) => state.setGroups);
  const groups = useAppStore((state) => state.groups);

  const syncGroupsMutation = useMutation({
    mutationFn: async (instanceName: string) => {
      const response = await makeRequest(`/group/fetchAllGroups/${instanceName}`);
      
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to sync groups');
      }

      const apiGroups = response.data;
      if (!Array.isArray(apiGroups)) {
        throw new Error('Invalid groups response format');
      }

      // Process and merge groups
      const existingIds = new Set(groups.map(g => g.id));
      const newGroups: Group[] = [];

      apiGroups.forEach((group: any) => {
        if (!group.id || existingIds.has(group.id)) return;

        newGroups.push({
          id: group.id,
          name: group.subject || group.name || 'Grupo sem nome',
          participants: group.participants?.length || 0,
          description: group.desc || '',
          createdAt: new Date(),
          updatedAt: new Date(),
          isActive: true,
        });
      });

      if (newGroups.length > 0) {
        setGroups([...groups, ...newGroups]);
      }

      return { newGroups: newGroups.length, total: apiGroups.length };
    },
  });

  return {
    syncGroups: syncGroupsMutation.mutate,
    isSyncing: syncGroupsMutation.isPending,
    syncError: syncGroupsMutation.error,
    syncResult: syncGroupsMutation.data,
  };
};

// Hook for sending messages
export const useMessageSender = () => {
  const { makeRequest } = useAdvancedApi();

  const sendMessageMutation = useMutation({
    mutationFn: async ({
      instanceName,
      recipient,
      message,
      mediaFile,
      options = {},
    }: {
      instanceName: string;
      recipient: string;
      message?: string;
      mediaFile?: any;
      options?: any;
    }) => {
      let endpoint: string;
      let payload: any;

      if (mediaFile && mediaFile.file) {
        // Convert file to base64
        const base64 = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            const result = reader.result as string;
            resolve(result.split(',')[1]);
          };
          reader.onerror = reject;
          reader.readAsDataURL(mediaFile.file);
        });

        if (mediaFile.type === 'audio') {
          endpoint = `/message/sendWhatsAppAudio/${instanceName}`;
          payload = {
            number: recipient,
            audio: base64,
            options,
          };
        } else {
          endpoint = `/message/sendMedia/${instanceName}`;
          payload = {
            number: recipient,
            mediatype: mediaFile.type === 'application' ? 'document' : mediaFile.type,
            mimetype: mediaFile.file.type,
            caption: message,
            media: base64,
            fileName: mediaFile.file.name,
            options,
          };
        }
      } else {
        endpoint = `/message/sendText/${instanceName}`;
        payload = {
          number: recipient,
          text: message,
          options,
        };
      }

      const response = await makeRequest(endpoint, {
        method: 'POST',
        body: JSON.stringify(payload),
      });

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to send message');
      }

      return response.data;
    },
  });

  return {
    sendMessage: sendMessageMutation.mutate,
    isSending: sendMessageMutation.isPending,
    sendError: sendMessageMutation.error,
  };
};
