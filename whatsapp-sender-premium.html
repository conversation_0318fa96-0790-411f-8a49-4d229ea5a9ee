<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Sender Premium - Sistema Completo</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/papaparse@5.4.1/papaparse.min.js"></script>
    <style>
        .animate-fade-in { animation: fadeIn 0.5s ease-out; }
        .animate-slide-up { animation: slideUp 0.3s ease-out; }
        .animate-pulse-slow { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
        .loading { animation: spin 1s linear infinite; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .gradient-success { background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%); }
        .gradient-warning { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .gradient-danger { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .glass-effect { backdrop-filter: blur(10px); background: rgba(255, 255, 255, 0.9); }
        .shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
        .shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
        
        @keyframes fadeIn { 
            from { opacity: 0; transform: translateY(20px); } 
            to { opacity: 1; transform: translateY(0); } 
        }
        @keyframes slideUp { 
            from { opacity: 0; transform: translateY(10px); } 
            to { opacity: 1; transform: translateY(0); } 
        }
        @keyframes spin { 
            from { transform: rotate(0deg); } 
            to { transform: rotate(360deg); } 
        }
        
        /* Scrollbar personalizada */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #f1f5f9; border-radius: 4px; }
        ::-webkit-scrollbar-thumb { background: #cbd5e1; border-radius: 4px; }
        ::-webkit-scrollbar-thumb:hover { background: #94a3b8; }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useCallback, useRef } = React;

        // Sistema de armazenamento aprimorado
        const Storage = {
            save: (key, data) => {
                try {
                    localStorage.setItem(`whatsapp_premium_${key}`, JSON.stringify(data));
                    console.log(`✅ Dados salvos: ${key}`, data);
                    return true;
                } catch (e) {
                    console.error(`❌ Erro ao salvar ${key}:`, e);
                    return false;
                }
            },
            load: (key, defaultValue = null) => {
                try {
                    const saved = localStorage.getItem(`whatsapp_premium_${key}`);
                    const data = saved ? JSON.parse(saved) : defaultValue;
                    console.log(`📂 Dados carregados: ${key}`, data);
                    return data;
                } catch (e) {
                    console.error(`❌ Erro ao carregar ${key}:`, e);
                    return defaultValue;
                }
            },
            clear: (key) => {
                try {
                    localStorage.removeItem(`whatsapp_premium_${key}`);
                    console.log(`🗑️ Dados removidos: ${key}`);
                    return true;
                } catch (e) {
                    console.error(`❌ Erro ao remover ${key}:`, e);
                    return false;
                }
            }
        };

        // Sistema de notificações aprimorado
        const useNotification = () => {
            const [notifications, setNotifications] = useState([]);

            const addNotification = useCallback((message, type = 'info', duration = 5000) => {
                const id = Date.now() + Math.random();
                const notification = { id, message, type, duration };
                
                console.log(`🔔 Notificação: [${type.toUpperCase()}] ${message}`);
                setNotifications(prev => [...prev, notification]);
                
                setTimeout(() => {
                    setNotifications(prev => prev.filter(n => n.id !== id));
                }, duration);
            }, []);

            const removeNotification = useCallback((id) => {
                setNotifications(prev => prev.filter(n => n.id !== id));
            }, []);

            return { notifications, addNotification, removeNotification };
        };

        // Sistema de API aprimorado
        const useAPI = () => {
            const [loading, setLoading] = useState(false);
            const { addNotification } = useNotification();

            const makeRequest = useCallback(async (endpoint, options = {}) => {
                const settings = Storage.load('settings', {});
                
                if (!settings.baseUrl || !settings.apiKey) {
                    throw new Error('Configure a URL Base e API Key nas Configurações');
                }

                const url = `${settings.baseUrl.replace(/\/$/, '')}/${endpoint.replace(/^\//, '')}`;
                
                console.log(`🌐 Fazendo requisição para: ${url}`);
                console.log(`🔑 Headers:`, { 'apikey': settings.apiKey.substring(0, 8) + '...' });
                
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': settings.apiKey,
                        ...options.headers,
                    },
                });

                console.log(`📡 Resposta: ${response.status} ${response.statusText}`);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error(`❌ Erro da API:`, errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                console.log(`📦 Dados recebidos:`, data);
                return data;
            }, []);

            const fetchInstances = useCallback(async () => {
                setLoading(true);
                try {
                    const data = await makeRequest('/instance/fetchInstances');
                    console.log('🖥️ Instâncias brutas da API:', data);
                    
                    let instances = [];
                    
                    if (Array.isArray(data)) {
                        instances = data;
                    } else if (data.instances && Array.isArray(data.instances)) {
                        instances = data.instances;
                    } else if (data.data && Array.isArray(data.data)) {
                        instances = data.data;
                    }

                    const normalizedInstances = instances.map(inst => {
                        const instanceData = inst.instance || inst;
                        
                        return {
                            id: instanceData.instanceName || instanceData.name || inst.instanceName || Math.random().toString(),
                            name: instanceData.instanceName || instanceData.name || inst.instanceName || 'Sem nome',
                            status: instanceData.state || inst.state || inst.connectionStatus || 'close',
                            profileName: instanceData.profileName || inst.profileName || null,
                            profilePictureUrl: instanceData.profilePictureUrl || inst.profilePictureUrl || null
                        };
                    });

                    console.log('✅ Instâncias normalizadas:', normalizedInstances);
                    Storage.save('instances', normalizedInstances);
                    return normalizedInstances;
                } catch (error) {
                    console.error('❌ Erro ao buscar instâncias:', error);
                    addNotification('Erro ao buscar instâncias: ' + error.message, 'error');
                    throw error;
                } finally {
                    setLoading(false);
                }
            }, [makeRequest, addNotification]);

            const createInstance = useCallback(async (name) => {
                setLoading(true);
                try {
                    const result = await makeRequest('/instance/create', {
                        method: 'POST',
                        body: JSON.stringify({ 
                            instanceName: name, 
                            qrcode: true,
                            integration: 'WHATSAPP-BAILEYS'
                        })
                    });
                    
                    addNotification(`Instância "${name}" criada com sucesso!`, 'success');
                    return result;
                } catch (error) {
                    console.error('❌ Erro ao criar instância:', error);
                    addNotification('Erro ao criar instância: ' + error.message, 'error');
                    throw error;
                } finally {
                    setLoading(false);
                }
            }, [makeRequest, addNotification]);

            const getQRCode = useCallback(async (instanceName) => {
                setLoading(true);
                try {
                    const result = await makeRequest(`/instance/connect/${instanceName}`);
                    console.log('📱 QR Code recebido:', result);
                    
                    let qrString = '';
                    if (typeof result === 'string') {
                        qrString = result;
                    } else if (result.base64) {
                        qrString = result.base64;
                    } else if (result.qrcode) {
                        qrString = result.qrcode;
                    } else if (result.code) {
                        qrString = result.code;
                    }

                    if (qrString) {
                        const qrImage = qrString.startsWith('data:image') ? qrString : `data:image/png;base64,${qrString}`;
                        return qrImage;
                    } else {
                        throw new Error('QR Code não encontrado na resposta');
                    }
                } catch (error) {
                    console.error('❌ Erro ao obter QR Code:', error);
                    addNotification('Erro ao obter QR Code: ' + error.message, 'error');
                    throw error;
                } finally {
                    setLoading(false);
                }
            }, [makeRequest, addNotification]);

            const getInstanceStatus = useCallback(async (instanceName) => {
                try {
                    const result = await makeRequest(`/instance/connectionState/${instanceName}`);
                    console.log('📊 Status da instância:', result);
                    return result;
                } catch (error) {
                    console.error('❌ Erro ao verificar status:', error);
                    addNotification('Erro ao verificar status: ' + error.message, 'error');
                    throw error;
                }
            }, [makeRequest, addNotification]);

            const sendMessage = useCallback(async (instanceName, number, text) => {
                try {
                    console.log(`📤 Enviando mensagem para ${number} via ${instanceName}:`, text);
                    
                    const result = await makeRequest(`/message/sendText/${instanceName}`, {
                        method: 'POST',
                        body: JSON.stringify({
                            number: number,
                            text: text,
                            options: {
                                delay: 1000
                            }
                        })
                    });
                    
                    console.log('✅ Mensagem enviada com sucesso:', result);
                    return result;
                } catch (error) {
                    console.error(`❌ Erro ao enviar mensagem para ${number}:`, error);
                    throw error;
                }
            }, [makeRequest]);

            const testConnection = useCallback(async () => {
                setLoading(true);
                try {
                    const instances = await fetchInstances();
                    addNotification('Conexão testada com sucesso!', 'success');
                    return { success: true, data: instances };
                } catch (error) {
                    addNotification('Erro na conexão: ' + error.message, 'error');
                    return { success: false, error: error.message };
                }
            }, [fetchInstances, addNotification]);

            return {
                loading,
                fetchInstances,
                createInstance,
                getQRCode,
                getInstanceStatus,
                sendMessage,
                testConnection
            };
        };

        // Componentes UI Premium
        const Card = ({ children, className = '', gradient = false, hover = true }) => (
            <div className={`
                bg-white rounded-2xl shadow-lg border border-gray-100 p-6 transition-all duration-300
                ${hover ? 'hover:shadow-xl hover:-translate-y-1' : ''}
                ${gradient ? 'bg-gradient-to-br from-white to-gray-50' : ''}
                ${className}
            `}>
                {children}
            </div>
        );

        const Button = ({
            children,
            onClick,
            disabled,
            variant = 'primary',
            size = 'md',
            className = '',
            loading = false,
            icon = null,
            fullWidth = false
        }) => {
            const variants = {
                primary: 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl',
                secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-800 shadow-md hover:shadow-lg',
                success: 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl',
                danger: 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-lg hover:shadow-xl',
                warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white shadow-lg hover:shadow-xl',
                outline: 'border-2 border-gray-300 hover:border-gray-400 bg-white hover:bg-gray-50 text-gray-700 shadow-md hover:shadow-lg'
            };

            const sizes = {
                sm: 'px-3 py-1.5 text-sm',
                md: 'px-6 py-2.5 text-sm',
                lg: 'px-8 py-3 text-base',
                xl: 'px-10 py-4 text-lg'
            };

            return (
                <button
                    onClick={onClick}
                    disabled={disabled || loading}
                    className={`
                        inline-flex items-center justify-center gap-2 rounded-xl font-semibold
                        transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50
                        transform hover:scale-105 active:scale-95
                        ${variants[variant]} ${sizes[size]} ${fullWidth ? 'w-full' : ''} ${className}
                    `}
                >
                    {loading && <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full loading"></div>}
                    {icon && !loading && <span className="text-lg">{icon}</span>}
                    {children}
                </button>
            );
        };

        const Badge = ({ children, variant = 'default', size = 'md' }) => {
            const variants = {
                default: 'bg-blue-100 text-blue-800 border border-blue-200',
                success: 'bg-green-100 text-green-800 border border-green-200',
                error: 'bg-red-100 text-red-800 border border-red-200',
                warning: 'bg-yellow-100 text-yellow-800 border border-yellow-200',
                info: 'bg-cyan-100 text-cyan-800 border border-cyan-200'
            };

            const sizes = {
                sm: 'px-2 py-0.5 text-xs',
                md: 'px-2.5 py-0.5 text-xs',
                lg: 'px-3 py-1 text-sm'
            };

            return (
                <span className={`inline-flex items-center rounded-full font-medium ${variants[variant]} ${sizes[size]}`}>
                    {children}
                </span>
            );
        };

        const Input = ({ label, error, icon, ...props }) => (
            <div className="space-y-2">
                {label && (
                    <label className="block text-sm font-semibold text-gray-700">
                        {label}
                    </label>
                )}
                <div className="relative">
                    {icon && (
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span className="text-gray-400 text-lg">{icon}</span>
                        </div>
                    )}
                    <input
                        {...props}
                        className={`
                            w-full px-4 py-3 border-2 rounded-xl transition-all duration-200
                            focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500
                            ${icon ? 'pl-12' : ''}
                            ${error ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20' : 'border-gray-200'}
                            ${props.disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white hover:border-gray-300'}
                        `}
                    />
                </div>
                {error && <p className="text-sm text-red-600 font-medium">{error}</p>}
            </div>
        );

        const Textarea = ({ label, error, ...props }) => (
            <div className="space-y-2">
                {label && (
                    <label className="block text-sm font-semibold text-gray-700">
                        {label}
                    </label>
                )}
                <textarea
                    {...props}
                    className={`
                        w-full px-4 py-3 border-2 rounded-xl transition-all duration-200
                        focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 resize-none
                        ${error ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20' : 'border-gray-200'}
                        ${props.disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white hover:border-gray-300'}
                    `}
                />
                {error && <p className="text-sm text-red-600 font-medium">{error}</p>}
            </div>
        );

        const Select = ({ label, error, children, ...props }) => (
            <div className="space-y-2">
                {label && (
                    <label className="block text-sm font-semibold text-gray-700">
                        {label}
                    </label>
                )}
                <select
                    {...props}
                    className={`
                        w-full px-4 py-3 border-2 rounded-xl transition-all duration-200
                        focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500
                        ${error ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20' : 'border-gray-200'}
                        ${props.disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white hover:border-gray-300'}
                    `}
                >
                    {children}
                </select>
                {error && <p className="text-sm text-red-600 font-medium">{error}</p>}
            </div>
        );

        // Componente de Notificações Premium
        const NotificationContainer = ({ notifications, removeNotification }) => (
            <div className="fixed top-4 right-4 z-50 space-y-3 max-w-sm">
                {notifications.map(notification => (
                    <div
                        key={notification.id}
                        className={`
                            p-4 rounded-xl shadow-2xl glass-effect border-l-4 animate-slide-up
                            ${notification.type === 'success' ? 'border-green-500 bg-green-50/90' : ''}
                            ${notification.type === 'error' ? 'border-red-500 bg-red-50/90' : ''}
                            ${notification.type === 'warning' ? 'border-yellow-500 bg-yellow-50/90' : ''}
                            ${notification.type === 'info' ? 'border-blue-500 bg-blue-50/90' : ''}
                        `}
                    >
                        <div className="flex items-start gap-3">
                            <span className="text-xl flex-shrink-0 mt-0.5">
                                {notification.type === 'success' && '✅'}
                                {notification.type === 'error' && '❌'}
                                {notification.type === 'warning' && '⚠️'}
                                {notification.type === 'info' && 'ℹ️'}
                            </span>
                            <div className="flex-1">
                                <p className="text-sm font-semibold text-gray-800 leading-relaxed">
                                    {notification.message}
                                </p>
                            </div>
                            <button
                                onClick={() => removeNotification(notification.id)}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                            >
                                <span className="text-lg">×</span>
                            </button>
                        </div>
                    </div>
                ))}
            </div>
        );

        // Modal Premium
        const Modal = ({ isOpen, onClose, title, children, size = 'md' }) => {
            if (!isOpen) return null;

            const sizes = {
                sm: 'max-w-md',
                md: 'max-w-lg',
                lg: 'max-w-2xl',
                xl: 'max-w-4xl'
            };

            return (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                    <div className={`bg-white rounded-2xl shadow-2xl w-full ${sizes[size]} max-h-[90vh] overflow-hidden animate-slide-up`}>
                        <div className="flex justify-between items-center p-6 border-b border-gray-100">
                            <h3 className="text-xl font-bold text-gray-900">{title}</h3>
                            <button
                                onClick={onClose}
                                className="text-gray-400 hover:text-gray-600 transition-colors p-1"
                            >
                                <span className="text-2xl">×</span>
                            </button>
                        </div>
                        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                            {children}
                        </div>
                    </div>
                </div>
            );
        };

        // Componente de Contatos com Importação CSV
        const Contacts = () => {
            const [contacts, setContacts] = useState(() => Storage.load('contacts', []));
            const [newContact, setNewContact] = useState({ name: '', phone: '', tags: '' });
            const [searchTerm, setSearchTerm] = useState('');
            const [editingContact, setEditingContact] = useState(null);
            const [isImportModalOpen, setIsImportModalOpen] = useState(false);
            const [csvData, setCsvData] = useState([]);
            const [csvHeaders, setCsvHeaders] = useState([]);
            const [fieldMapping, setFieldMapping] = useState({});
            const fileInputRef = useRef(null);
            const { addNotification } = useNotification();

            const filteredContacts = contacts.filter(contact =>
                contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                contact.phone.includes(searchTerm) ||
                (contact.tags && contact.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))
            );

            const validateContact = (contact) => {
                if (!contact.name?.trim()) {
                    addNotification('Nome é obrigatório', 'error');
                    return false;
                }
                if (!contact.phone?.trim()) {
                    addNotification('Telefone é obrigatório', 'error');
                    return false;
                }
                const cleanPhone = contact.phone.replace(/\D/g, '');
                if (cleanPhone.length < 10 || cleanPhone.length > 15) {
                    addNotification('Telefone deve ter entre 10 e 15 dígitos', 'error');
                    return false;
                }
                return true;
            };

            const addContact = () => {
                if (!validateContact(newContact)) return;

                const contact = {
                    id: Date.now().toString(),
                    name: newContact.name.trim(),
                    phone: newContact.phone.replace(/\D/g, ''),
                    tags: newContact.tags.split(',').map(t => t.trim()).filter(Boolean),
                    createdAt: new Date().toISOString()
                };

                const updatedContacts = [...contacts, contact];
                setContacts(updatedContacts);
                Storage.save('contacts', updatedContacts);
                setNewContact({ name: '', phone: '', tags: '' });
                addNotification('Contato adicionado com sucesso!', 'success');
            };

            const updateContact = () => {
                if (!validateContact(editingContact)) return;

                const updatedContacts = contacts.map(c =>
                    c.id === editingContact.id
                        ? {
                            ...editingContact,
                            name: editingContact.name.trim(),
                            phone: editingContact.phone.replace(/\D/g, ''),
                            tags: typeof editingContact.tags === 'string'
                                ? editingContact.tags.split(',').map(t => t.trim()).filter(Boolean)
                                : editingContact.tags,
                            updatedAt: new Date().toISOString()
                        }
                        : c
                );

                setContacts(updatedContacts);
                Storage.save('contacts', updatedContacts);
                setEditingContact(null);
                addNotification('Contato atualizado com sucesso!', 'success');
            };

            const deleteContact = (id) => {
                if (confirm('Tem certeza que deseja excluir este contato?')) {
                    const updatedContacts = contacts.filter(c => c.id !== id);
                    setContacts(updatedContacts);
                    Storage.save('contacts', updatedContacts);
                    addNotification('Contato excluído', 'success');
                }
            };

            const handleFileUpload = (event) => {
                const file = event.target.files[0];
                if (!file) return;

                if (!file.name.toLowerCase().endsWith('.csv')) {
                    addNotification('Por favor, selecione um arquivo CSV', 'error');
                    return;
                }

                Papa.parse(file, {
                    header: true,
                    skipEmptyLines: true,
                    complete: (results) => {
                        if (results.errors.length > 0) {
                            console.error('Erros no CSV:', results.errors);
                            addNotification('Erro ao ler arquivo CSV: ' + results.errors[0].message, 'error');
                            return;
                        }

                        if (results.data.length === 0) {
                            addNotification('Arquivo CSV está vazio', 'error');
                            return;
                        }

                        const headers = Object.keys(results.data[0]);
                        setCsvHeaders(headers);
                        setCsvData(results.data);

                        // Mapeamento automático inteligente
                        const autoMapping = {};
                        headers.forEach(header => {
                            const lowerHeader = header.toLowerCase();
                            if (lowerHeader.includes('nome') || lowerHeader.includes('name')) {
                                autoMapping.name = header;
                            } else if (lowerHeader.includes('telefone') || lowerHeader.includes('phone') || lowerHeader.includes('celular')) {
                                autoMapping.phone = header;
                            } else if (lowerHeader.includes('tag') || lowerHeader.includes('categoria')) {
                                autoMapping.tags = header;
                            }
                        });

                        setFieldMapping(autoMapping);
                        setIsImportModalOpen(true);
                        addNotification(`${results.data.length} registros encontrados no CSV`, 'success');
                    },
                    error: (error) => {
                        console.error('Erro ao processar CSV:', error);
                        addNotification('Erro ao processar arquivo CSV', 'error');
                    }
                });

                // Limpar input
                event.target.value = '';
            };

            const importContacts = () => {
                if (!fieldMapping.name || !fieldMapping.phone) {
                    addNotification('Mapeie pelo menos os campos Nome e Telefone', 'error');
                    return;
                }

                const existingPhones = new Set(contacts.map(c => c.phone));
                let importedCount = 0;
                let skippedCount = 0;

                const newContacts = csvData.map((row, index) => {
                    const name = row[fieldMapping.name]?.trim();
                    const phone = row[fieldMapping.phone]?.replace(/\D/g, '');

                    if (!name || !phone) {
                        skippedCount++;
                        return null;
                    }

                    if (existingPhones.has(phone)) {
                        skippedCount++;
                        return null;
                    }

                    existingPhones.add(phone);
                    importedCount++;

                    // Criar objeto com todos os campos do CSV
                    const contact = {
                        id: `csv_${Date.now()}_${index}`,
                        name: name,
                        phone: phone,
                        tags: fieldMapping.tags ?
                            row[fieldMapping.tags]?.split(',').map(t => t.trim()).filter(Boolean) || [] :
                            ['importado'],
                        createdAt: new Date().toISOString(),
                        source: 'csv'
                    };

                    // Adicionar campos extras do CSV
                    Object.keys(row).forEach(key => {
                        if (key !== fieldMapping.name && key !== fieldMapping.phone && key !== fieldMapping.tags) {
                            contact[key.toLowerCase().replace(/\s+/g, '_')] = row[key];
                        }
                    });

                    return contact;
                }).filter(Boolean);

                if (newContacts.length > 0) {
                    const updatedContacts = [...contacts, ...newContacts];
                    setContacts(updatedContacts);
                    Storage.save('contacts', updatedContacts);

                    // Salvar headers para uso no disparador
                    const allFields = new Set();
                    updatedContacts.forEach(contact => {
                        Object.keys(contact).forEach(key => {
                            if (!['id', 'createdAt', 'updatedAt', 'source'].includes(key)) {
                                allFields.add(key);
                            }
                        });
                    });
                    Storage.save('contactFields', Array.from(allFields));
                }

                setIsImportModalOpen(false);
                setCsvData([]);
                setCsvHeaders([]);
                setFieldMapping({});

                addNotification(
                    `Importação concluída! ${importedCount} contatos importados, ${skippedCount} ignorados (duplicados ou inválidos)`,
                    'success'
                );
            };

            return (
                <div className="space-y-6">
                    <Card gradient>
                        <div className="flex items-center justify-between mb-6">
                            <div className="flex items-center gap-4">
                                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
                                    <span className="text-white text-2xl">👥</span>
                                </div>
                                <div>
                                    <h2 className="text-2xl font-bold text-gray-900">Gerenciar Contatos</h2>
                                    <p className="text-gray-600">{contacts.length} contatos cadastrados</p>
                                </div>
                            </div>
                            <div className="flex gap-3">
                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept=".csv"
                                    onChange={handleFileUpload}
                                    className="hidden"
                                />
                                <Button
                                    onClick={() => fileInputRef.current?.click()}
                                    variant="success"
                                    icon="📁"
                                >
                                    Importar CSV
                                </Button>
                            </div>
                        </div>

                        {/* Adicionar/Editar Contato */}
                        <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 mb-6">
                            <h3 className="font-bold mb-4 text-lg">
                                {editingContact ? '✏️ Editar Contato' : '➕ Adicionar Novo Contato'}
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <Input
                                    label="Nome Completo"
                                    placeholder="João Silva"
                                    icon="👤"
                                    value={editingContact ? editingContact.name : newContact.name}
                                    onChange={(e) => {
                                        if (editingContact) {
                                            setEditingContact({...editingContact, name: e.target.value});
                                        } else {
                                            setNewContact({...newContact, name: e.target.value});
                                        }
                                    }}
                                />
                                <Input
                                    label="Telefone"
                                    placeholder="5511999998888"
                                    icon="📱"
                                    value={editingContact ? editingContact.phone : newContact.phone}
                                    onChange={(e) => {
                                        if (editingContact) {
                                            setEditingContact({...editingContact, phone: e.target.value});
                                        } else {
                                            setNewContact({...newContact, phone: e.target.value});
                                        }
                                    }}
                                />
                                <Input
                                    label="Tags"
                                    placeholder="cliente, vip, sp"
                                    icon="🏷️"
                                    value={editingContact ?
                                        (typeof editingContact.tags === 'string' ? editingContact.tags : editingContact.tags?.join(', ')) :
                                        newContact.tags
                                    }
                                    onChange={(e) => {
                                        if (editingContact) {
                                            setEditingContact({...editingContact, tags: e.target.value});
                                        } else {
                                            setNewContact({...newContact, tags: e.target.value});
                                        }
                                    }}
                                />
                                <div className="flex flex-col gap-2">
                                    <label className="text-sm font-semibold text-gray-700">Ações</label>
                                    <div className="flex gap-2">
                                        <Button
                                            onClick={editingContact ? updateContact : addContact}
                                            variant="primary"
                                            icon={editingContact ? "💾" : "➕"}
                                            fullWidth
                                        >
                                            {editingContact ? 'Salvar' : 'Adicionar'}
                                        </Button>
                                        {editingContact && (
                                            <Button
                                                onClick={() => setEditingContact(null)}
                                                variant="outline"
                                                icon="❌"
                                            >
                                                Cancelar
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Busca */}
                        <div className="mb-6">
                            <Input
                                placeholder="🔍 Buscar contatos por nome, telefone ou tag..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                icon="🔍"
                            />
                        </div>

                        {/* Lista de Contatos */}
                        <div className="bg-white rounded-2xl border border-gray-200 overflow-hidden">
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gradient-to-r from-gray-50 to-blue-50">
                                        <tr>
                                            <th className="text-left p-4 font-bold text-gray-700">Nome</th>
                                            <th className="text-left p-4 font-bold text-gray-700">Telefone</th>
                                            <th className="text-left p-4 font-bold text-gray-700">Tags</th>
                                            <th className="text-center p-4 font-bold text-gray-700">Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {filteredContacts.map((contact, index) => (
                                            <tr key={contact.id} className={`border-b border-gray-100 hover:bg-gray-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`}>
                                                <td className="p-4">
                                                    <div className="font-semibold text-gray-900">{contact.name}</div>
                                                    {contact.source === 'csv' && (
                                                        <Badge variant="info" size="sm">Importado</Badge>
                                                    )}
                                                </td>
                                                <td className="p-4 font-mono text-sm text-gray-600">{contact.phone}</td>
                                                <td className="p-4">
                                                    <div className="flex gap-1 flex-wrap">
                                                        {contact.tags?.map(tag => (
                                                            <Badge key={tag} variant="default" size="sm">{tag}</Badge>
                                                        ))}
                                                    </div>
                                                </td>
                                                <td className="p-4 text-center">
                                                    <div className="flex justify-center gap-2">
                                                        <Button
                                                            onClick={() => setEditingContact({
                                                                ...contact,
                                                                tags: contact.tags?.join(', ') || ''
                                                            })}
                                                            variant="outline"
                                                            size="sm"
                                                            icon="✏️"
                                                        >
                                                            Editar
                                                        </Button>
                                                        <Button
                                                            onClick={() => deleteContact(contact.id)}
                                                            variant="danger"
                                                            size="sm"
                                                            icon="🗑️"
                                                        >
                                                            Excluir
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>

                                {filteredContacts.length === 0 && (
                                    <div className="text-center py-12 text-gray-500">
                                        <div className="text-6xl mb-4">📭</div>
                                        <h3 className="text-lg font-semibold mb-2">
                                            {searchTerm ? 'Nenhum contato encontrado' : 'Nenhum contato cadastrado'}
                                        </h3>
                                        <p className="text-sm">
                                            {searchTerm ? 'Tente uma busca diferente' : 'Adicione contatos manualmente ou importe um arquivo CSV'}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </Card>

                    {/* Modal de Importação CSV */}
                    <Modal
                        isOpen={isImportModalOpen}
                        onClose={() => setIsImportModalOpen(false)}
                        title="📁 Importar Contatos do CSV"
                        size="lg"
                    >
                        <div className="space-y-6">
                            <div className="bg-blue-50 rounded-xl p-4">
                                <h4 className="font-semibold text-blue-900 mb-2">📋 Dados Encontrados</h4>
                                <p className="text-blue-700 text-sm">
                                    <strong>{csvData.length}</strong> registros encontrados com <strong>{csvHeaders.length}</strong> colunas
                                </p>
                            </div>

                            <div>
                                <h4 className="font-semibold mb-4">🔗 Mapeamento de Campos</h4>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <Select
                                        label="Nome *"
                                        value={fieldMapping.name || ''}
                                        onChange={(e) => setFieldMapping({...fieldMapping, name: e.target.value})}
                                    >
                                        <option value="">Selecione a coluna do nome</option>
                                        {csvHeaders.map(header => (
                                            <option key={header} value={header}>{header}</option>
                                        ))}
                                    </Select>

                                    <Select
                                        label="Telefone *"
                                        value={fieldMapping.phone || ''}
                                        onChange={(e) => setFieldMapping({...fieldMapping, phone: e.target.value})}
                                    >
                                        <option value="">Selecione a coluna do telefone</option>
                                        {csvHeaders.map(header => (
                                            <option key={header} value={header}>{header}</option>
                                        ))}
                                    </Select>

                                    <Select
                                        label="Tags (opcional)"
                                        value={fieldMapping.tags || ''}
                                        onChange={(e) => setFieldMapping({...fieldMapping, tags: e.target.value})}
                                    >
                                        <option value="">Selecione a coluna das tags</option>
                                        {csvHeaders.map(header => (
                                            <option key={header} value={header}>{header}</option>
                                        ))}
                                    </Select>
                                </div>
                            </div>

                            <div className="bg-yellow-50 rounded-xl p-4">
                                <h4 className="font-semibold text-yellow-900 mb-2">💡 Campos Extras</h4>
                                <p className="text-yellow-700 text-sm mb-2">
                                    Todas as outras colunas do CSV serão importadas como campos personalizados e poderão ser usadas no disparador como variáveis.
                                </p>
                                <div className="flex flex-wrap gap-2">
                                    {csvHeaders.filter(h => h !== fieldMapping.name && h !== fieldMapping.phone && h !== fieldMapping.tags).map(header => (
                                        <Badge key={header} variant="warning" size="sm">
                                            {`{{${header.toLowerCase().replace(/\s+/g, '_')}}}`}
                                        </Badge>
                                    ))}
                                </div>
                            </div>

                            {csvData.length > 0 && (
                                <div>
                                    <h4 className="font-semibold mb-2">👀 Prévia dos Dados</h4>
                                    <div className="bg-gray-50 rounded-xl p-4 max-h-40 overflow-auto">
                                        <table className="w-full text-sm">
                                            <thead>
                                                <tr>
                                                    {csvHeaders.slice(0, 5).map(header => (
                                                        <th key={header} className="text-left p-2 font-semibold text-gray-700">
                                                            {header}
                                                        </th>
                                                    ))}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {csvData.slice(0, 3).map((row, index) => (
                                                    <tr key={index}>
                                                        {csvHeaders.slice(0, 5).map(header => (
                                                            <td key={header} className="p-2 text-gray-600">
                                                                {row[header]}
                                                            </td>
                                                        ))}
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            )}

                            <div className="flex justify-end gap-3 pt-4 border-t">
                                <Button
                                    onClick={() => setIsImportModalOpen(false)}
                                    variant="outline"
                                >
                                    Cancelar
                                </Button>
                                <Button
                                    onClick={importContacts}
                                    variant="success"
                                    icon="📥"
                                    disabled={!fieldMapping.name || !fieldMapping.phone}
                                >
                                    Importar Contatos
                                </Button>
                            </div>
                        </div>
                    </Modal>
                </div>
            );
        };

        // Componente de Disparador Corrigido
        const Campaign = () => {
            const [campaignData, setCampaignData] = useState({
                instance: '',
                message: '',
                tagFilter: ''
            });
            const [isRunning, setIsRunning] = useState(false);
            const [isPaused, setIsPaused] = useState(false);
            const [progress, setProgress] = useState({ current: 0, total: 0, success: 0, errors: 0 });
            const [campaignResults, setCampaignResults] = useState([]);
            const [abortController, setAbortController] = useState(null);

            const contacts = Storage.load('contacts', []);
            const instances = Storage.load('instances', []);
            const settings = Storage.load('settings', {});
            const contactFields = Storage.load('contactFields', ['name', 'phone', 'tags']);
            const { sendMessage } = useAPI();
            const { addNotification } = useNotification();

            const connectedInstances = instances.filter(i => i.status === 'open');
            const filteredContacts = campaignData.tagFilter
                ? contacts.filter(c => c.tags?.includes(campaignData.tagFilter))
                : contacts;

            // Obter todas as tags únicas
            const allTags = [...new Set(contacts.flatMap(c => c.tags || []))];

            const validateCampaign = () => {
                if (!campaignData.instance) {
                    addNotification('Selecione uma instância conectada', 'error');
                    return false;
                }
                if (!campaignData.message.trim()) {
                    addNotification('Digite uma mensagem para enviar', 'error');
                    return false;
                }
                if (filteredContacts.length === 0) {
                    addNotification('Nenhum contato selecionado para envio', 'error');
                    return false;
                }
                return true;
            };

            const startCampaign = async () => {
                if (!validateCampaign()) return;

                console.log('🚀 Iniciando campanha...');
                console.log('📋 Configuração:', {
                    instance: campaignData.instance,
                    totalContacts: filteredContacts.length,
                    message: campaignData.message
                });

                setIsRunning(true);
                setIsPaused(false);
                setProgress({ current: 0, total: filteredContacts.length, success: 0, errors: 0 });
                setCampaignResults([]);

                const controller = new AbortController();
                setAbortController(controller);

                const minDelay = parseFloat(settings.minDelay || '2') * 1000;
                const maxDelay = parseFloat(settings.maxDelay || '5') * 1000;
                const results = [];

                try {
                    for (let i = 0; i < filteredContacts.length; i++) {
                        if (controller.signal.aborted) {
                            console.log('⏹️ Campanha interrompida pelo usuário');
                            break;
                        }

                        const contact = filteredContacts[i];
                        console.log(`📤 Processando contato ${i + 1}/${filteredContacts.length}: ${contact.name}`);

                        setProgress(prev => ({ ...prev, current: i + 1 }));

                        try {
                            // Personalizar mensagem com todos os campos disponíveis
                            let personalizedMessage = campaignData.message;

                            // Substituir variáveis padrão
                            personalizedMessage = personalizedMessage.replace(/\{\{nome\}\}/gi, contact.name || '');
                            personalizedMessage = personalizedMessage.replace(/\{\{name\}\}/gi, contact.name || '');
                            personalizedMessage = personalizedMessage.replace(/\{\{phone\}\}/gi, contact.phone || '');
                            personalizedMessage = personalizedMessage.replace(/\{\{telefone\}\}/gi, contact.phone || '');
                            personalizedMessage = personalizedMessage.replace(/\{\{tags\}\}/gi, contact.tags?.join(', ') || '');

                            // Substituir campos dinâmicos do CSV
                            Object.keys(contact).forEach(key => {
                                if (!['id', 'createdAt', 'updatedAt', 'source'].includes(key)) {
                                    const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'gi');
                                    personalizedMessage = personalizedMessage.replace(regex, contact[key] || '');
                                }
                            });

                            console.log(`💬 Mensagem personalizada para ${contact.name}:`, personalizedMessage);

                            // Enviar mensagem
                            await sendMessage(campaignData.instance, contact.phone, personalizedMessage);

                            const result = {
                                contact: contact.name,
                                phone: contact.phone,
                                status: 'success',
                                message: personalizedMessage,
                                timestamp: new Date().toLocaleTimeString()
                            };

                            results.push(result);
                            setProgress(prev => ({ ...prev, success: prev.success + 1 }));
                            setCampaignResults(prev => [...prev, result]);

                            console.log(`✅ Mensagem enviada com sucesso para ${contact.name}`);

                        } catch (error) {
                            console.error(`❌ Erro ao enviar para ${contact.name}:`, error);

                            const result = {
                                contact: contact.name,
                                phone: contact.phone,
                                status: 'error',
                                error: error.message,
                                timestamp: new Date().toLocaleTimeString()
                            };

                            results.push(result);
                            setProgress(prev => ({ ...prev, errors: prev.errors + 1 }));
                            setCampaignResults(prev => [...prev, result]);
                        }

                        // Delay entre envios
                        if (i < filteredContacts.length - 1 && !controller.signal.aborted) {
                            const delay = Math.random() * (maxDelay - minDelay) + minDelay;
                            console.log(`⏱️ Aguardando ${Math.round(delay/1000)}s antes do próximo envio...`);
                            await new Promise(resolve => setTimeout(resolve, delay));
                        }
                    }
                } catch (error) {
                    console.error('❌ Erro geral na campanha:', error);
                    addNotification('Erro na execução da campanha: ' + error.message, 'error');
                }

                setIsRunning(false);
                setAbortController(null);

                // Salvar estatísticas
                const stats = {
                    total: filteredContacts.length,
                    success: results.filter(r => r.status === 'success').length,
                    errors: results.filter(r => r.status === 'error').length,
                    lastRun: new Date().toLocaleString('pt-BR'),
                    duration: Date.now()
                };
                Storage.save('stats', stats);

                console.log('📊 Estatísticas finais:', stats);

                addNotification(
                    `Campanha concluída! ${stats.success} sucessos, ${stats.errors} erros`,
                    stats.errors === 0 ? 'success' : 'warning'
                );
            };

            const stopCampaign = () => {
                if (abortController) {
                    abortController.abort();
                    setAbortController(null);
                }
                setIsRunning(false);
                setIsPaused(false);
                addNotification('Campanha interrompida pelo usuário', 'info');
            };

            const insertVariable = (variable) => {
                setCampaignData(prev => ({
                    ...prev,
                    message: prev.message + `{{${variable}}}`
                }));
            };

            return (
                <div className="space-y-6">
                    {/* Status da Campanha */}
                    {isRunning && (
                        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
                            <div className="flex justify-between items-center mb-6">
                                <div className="flex items-center gap-4">
                                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center animate-pulse-slow">
                                        <span className="text-white text-2xl">🚀</span>
                                    </div>
                                    <div>
                                        <h3 className="text-xl font-bold text-blue-900">
                                            Campanha em Andamento
                                        </h3>
                                        <p className="text-blue-700">
                                            Enviando para {progress.current} de {progress.total} contatos
                                        </p>
                                    </div>
                                </div>
                                <Button onClick={stopCampaign} variant="danger" icon="⏹️">
                                    Parar Campanha
                                </Button>
                            </div>

                            <div className="mb-6">
                                <div className="flex justify-between text-sm text-blue-700 mb-2">
                                    <span>Progresso: {progress.current}/{progress.total}</span>
                                    <span>{Math.round((progress.current / progress.total) * 100)}%</span>
                                </div>
                                <div className="w-full bg-blue-200 rounded-full h-3">
                                    <div
                                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500"
                                        style={{ width: `${(progress.current / progress.total) * 100}%` }}
                                    ></div>
                                </div>
                            </div>

                            <div className="grid grid-cols-2 gap-6 text-center">
                                <div className="bg-white/50 rounded-xl p-4">
                                    <p className="text-3xl font-bold text-green-600">{progress.success}</p>
                                    <p className="text-sm text-gray-600 font-semibold">Sucessos</p>
                                </div>
                                <div className="bg-white/50 rounded-xl p-4">
                                    <p className="text-3xl font-bold text-red-600">{progress.errors}</p>
                                    <p className="text-sm text-gray-600 font-semibold">Erros</p>
                                </div>
                            </div>
                        </Card>
                    )}

                    <Card gradient>
                        <div className="flex items-center gap-4 mb-6">
                            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center">
                                <span className="text-white text-2xl">🚀</span>
                            </div>
                            <div>
                                <h2 className="text-2xl font-bold text-gray-900">Disparador de Campanhas</h2>
                                <p className="text-gray-600">Envie mensagens personalizadas para seus contatos</p>
                            </div>
                        </div>

                        <div className="space-y-6">
                            {/* Seleção de Instância */}
                            <Select
                                label="Instância de Envio *"
                                value={campaignData.instance}
                                onChange={(e) => setCampaignData({...campaignData, instance: e.target.value})}
                                disabled={isRunning}
                            >
                                <option value="">Selecione uma instância conectada</option>
                                {connectedInstances.map(instance => (
                                    <option key={instance.id} value={instance.name}>
                                        {instance.name} ({instance.profileName || 'Conectado'})
                                    </option>
                                ))}
                            </Select>

                            {connectedInstances.length === 0 && (
                                <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                                    <p className="text-yellow-800 text-sm font-semibold">
                                        ⚠️ Nenhuma instância conectada. Configure uma instância na aba "Instâncias".
                                    </p>
                                </div>
                            )}

                            {/* Filtro de Tag */}
                            <Select
                                label="Filtrar Contatos por Tag (opcional)"
                                value={campaignData.tagFilter}
                                onChange={(e) => setCampaignData({...campaignData, tagFilter: e.target.value})}
                                disabled={isRunning}
                            >
                                <option value="">Todos os contatos</option>
                                {allTags.map(tag => (
                                    <option key={tag} value={tag}>{tag}</option>
                                ))}
                            </Select>

                            {/* Mensagem */}
                            <div>
                                <Textarea
                                    label="Mensagem *"
                                    value={campaignData.message}
                                    onChange={(e) => setCampaignData({...campaignData, message: e.target.value})}
                                    placeholder="Digite sua mensagem... Use as variáveis abaixo para personalizar"
                                    rows="6"
                                    disabled={isRunning}
                                />

                                {/* Variáveis Disponíveis */}
                                <div className="mt-4 p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl">
                                    <h4 className="font-semibold text-gray-800 mb-3">🏷️ Variáveis Disponíveis:</h4>
                                    <div className="flex flex-wrap gap-2">
                                        {contactFields.map(field => (
                                            <button
                                                key={field}
                                                onClick={() => insertVariable(field)}
                                                disabled={isRunning}
                                                className="px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-lg text-sm font-medium transition-colors disabled:opacity-50"
                                            >
                                                {`{{${field}}}`}
                                            </button>
                                        ))}
                                    </div>
                                    <p className="text-xs text-gray-600 mt-2">
                                        💡 Clique nas variáveis para inserir na mensagem. Campos do CSV importado também estão disponíveis.
                                    </p>
                                </div>
                            </div>

                            {/* Info dos Destinatários */}
                            <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6">
                                <div className="flex justify-between items-center">
                                    <div>
                                        <p className="text-lg font-bold text-gray-800">
                                            📊 Destinatários selecionados:
                                            <span className="text-blue-600 ml-2">{filteredContacts.length}</span>
                                        </p>
                                        {filteredContacts.length > 0 && (
                                            <p className="text-sm text-gray-600 mt-1">
                                                {filteredContacts.slice(0, 3).map(c => c.name).join(', ')}
                                                {filteredContacts.length > 3 && ` e mais ${filteredContacts.length - 3}...`}
                                            </p>
                                        )}
                                    </div>
                                    {filteredContacts.length > 0 && (
                                        <div className="text-right">
                                            <p className="text-sm text-gray-600">Tempo estimado:</p>
                                            <p className="text-lg font-bold text-gray-800">
                                                {Math.ceil(filteredContacts.length * 3.5 / 60)} minutos
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Botão de Envio */}
                            <Button
                                onClick={startCampaign}
                                disabled={isRunning || !campaignData.instance || !campaignData.message || filteredContacts.length === 0}
                                loading={isRunning}
                                variant="primary"
                                size="xl"
                                fullWidth
                                icon="🚀"
                            >
                                {isRunning ? (
                                    'Enviando Mensagens...'
                                ) : (
                                    `Iniciar Disparos para ${filteredContacts.length} Contatos`
                                )}
                            </Button>
                        </div>
                    </Card>

                    {/* Resultados da Campanha */}
                    {campaignResults.length > 0 && (
                        <Card>
                            <h3 className="text-xl font-bold mb-4 flex items-center gap-2">
                                <span>📋</span> Resultados da Campanha
                            </h3>
                            <div className="max-h-80 overflow-y-auto space-y-3">
                                {campaignResults.map((result, index) => (
                                    <div key={index} className={`
                                        p-4 rounded-xl border-l-4 transition-all duration-200
                                        ${result.status === 'success'
                                            ? 'bg-green-50 border-green-500'
                                            : 'bg-red-50 border-red-500'
                                        }
                                    `}>
                                        <div className="flex justify-between items-start">
                                            <div className="flex-1">
                                                <div className="flex items-center gap-2 mb-1">
                                                    <span className={result.status === 'success' ? 'text-green-600' : 'text-red-600'}>
                                                        {result.status === 'success' ? '✅' : '❌'}
                                                    </span>
                                                    <span className="font-semibold text-gray-800">{result.contact}</span>
                                                    <span className="text-sm text-gray-500">({result.phone})</span>
                                                </div>
                                                {result.error && (
                                                    <p className="text-red-600 text-sm mt-1">{result.error}</p>
                                                )}
                                                {result.message && result.status === 'success' && (
                                                    <p className="text-gray-600 text-sm mt-1 italic">"{result.message.substring(0, 100)}..."</p>
                                                )}
                                            </div>
                                            <span className="text-xs text-gray-500 ml-4">{result.timestamp}</span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </Card>
                    )}
                </div>
            );
        };

        // Componente de Configurações Premium
        const Settings = () => {
            const [settings, setSettings] = useState(() => Storage.load('settings', {
                baseUrl: '',
                apiKey: '',
                minDelay: '2',
                maxDelay: '5'
            }));
            const [errors, setErrors] = useState({});
            const { addNotification } = useNotification();

            const validateSettings = () => {
                const newErrors = {};

                if (!settings.baseUrl) {
                    newErrors.baseUrl = 'URL Base é obrigatória';
                } else if (!settings.baseUrl.startsWith('http')) {
                    newErrors.baseUrl = 'URL deve começar com http:// ou https://';
                }

                if (!settings.apiKey) {
                    newErrors.apiKey = 'API Key é obrigatória';
                } else if (settings.apiKey.length < 8) {
                    newErrors.apiKey = 'API Key deve ter pelo menos 8 caracteres';
                }

                if (parseFloat(settings.minDelay) < 1) {
                    newErrors.minDelay = 'Delay mínimo deve ser pelo menos 1 segundo';
                }

                if (parseFloat(settings.maxDelay) < parseFloat(settings.minDelay)) {
                    newErrors.maxDelay = 'Delay máximo deve ser maior que o mínimo';
                }

                setErrors(newErrors);
                return Object.keys(newErrors).length === 0;
            };

            const handleSave = () => {
                if (!validateSettings()) {
                    addNotification('Corrija os erros antes de salvar', 'error');
                    return;
                }

                if (Storage.save('settings', settings)) {
                    addNotification('Configurações salvas com sucesso!', 'success');
                } else {
                    addNotification('Erro ao salvar configurações', 'error');
                }
            };

            const handleInputChange = (field, value) => {
                setSettings(prev => ({ ...prev, [field]: value }));
                if (errors[field]) {
                    setErrors(prev => ({ ...prev, [field]: '' }));
                }
            };

            return (
                <div className="space-y-6">
                    <Card gradient>
                        <div className="flex items-center gap-4 mb-6">
                            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center">
                                <span className="text-white text-2xl">⚙️</span>
                            </div>
                            <div>
                                <h2 className="text-2xl font-bold text-gray-900">Configurações da API</h2>
                                <p className="text-gray-600">Configure sua Evolution API para começar a usar</p>
                            </div>
                        </div>

                        <div className="space-y-6">
                            <Input
                                label="URL Base da API *"
                                type="url"
                                value={settings.baseUrl}
                                onChange={(e) => handleInputChange('baseUrl', e.target.value)}
                                placeholder="http://localhost:8080"
                                error={errors.baseUrl}
                                icon="🌐"
                            />

                            <Input
                                label="API Key *"
                                type="password"
                                value={settings.apiKey}
                                onChange={(e) => handleInputChange('apiKey', e.target.value)}
                                placeholder="Sua chave da Evolution API"
                                error={errors.apiKey}
                                icon="🔑"
                            />

                            <div className="grid grid-cols-2 gap-4">
                                <Input
                                    label="Delay Mínimo (segundos)"
                                    type="number"
                                    min="1"
                                    step="0.1"
                                    value={settings.minDelay}
                                    onChange={(e) => handleInputChange('minDelay', e.target.value)}
                                    error={errors.minDelay}
                                    icon="⏱️"
                                />
                                <Input
                                    label="Delay Máximo (segundos)"
                                    type="number"
                                    min="1"
                                    step="0.1"
                                    value={settings.maxDelay}
                                    onChange={(e) => handleInputChange('maxDelay', e.target.value)}
                                    error={errors.maxDelay}
                                    icon="⏰"
                                />
                            </div>

                            <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-6">
                                <h4 className="font-bold text-yellow-800 mb-3 flex items-center gap-2">
                                    <span>💡</span> Dicas importantes
                                </h4>
                                <ul className="text-sm text-yellow-700 space-y-2">
                                    <li className="flex items-start gap-2">
                                        <span>•</span>
                                        <span>Certifique-se que sua Evolution API está rodando</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span>•</span>
                                        <span>Configure CORS_ORIGIN=* no .env da Evolution API</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span>•</span>
                                        <span>Use delays de 2-5 segundos para evitar bloqueios</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span>•</span>
                                        <span>Teste a conexão após salvar as configurações</span>
                                    </li>
                                </ul>
                            </div>

                            <Button
                                onClick={handleSave}
                                variant="primary"
                                size="lg"
                                fullWidth
                                icon="💾"
                            >
                                Salvar Configurações
                            </Button>
                        </div>
                    </Card>
                </div>
            );
        };

        // Componente de Teste de API Premium
        const ApiTest = () => {
            const [result, setResult] = useState(null);
            const { testConnection, loading } = useAPI();
            const { addNotification } = useNotification();
            const settings = Storage.load('settings', {});

            const handleTest = async () => {
                setResult(null);
                const testResult = await testConnection();
                setResult(testResult);
            };

            return (
                <div className="space-y-6">
                    <Card gradient>
                        <div className="flex items-center gap-4 mb-6">
                            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-2xl flex items-center justify-center">
                                <span className="text-white text-2xl">🔍</span>
                            </div>
                            <div>
                                <h2 className="text-2xl font-bold text-gray-900">Teste de Conexão API</h2>
                                <p className="text-gray-600">Verifique se sua Evolution API está funcionando</p>
                            </div>
                        </div>

                        <div className="space-y-6">
                            <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6">
                                <h3 className="font-bold mb-4 text-gray-800">📋 Configuração Atual</h3>
                                <div className="space-y-3">
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-600 font-medium">URL:</span>
                                        <span className="font-mono text-sm bg-white px-3 py-1 rounded-lg">
                                            {settings.baseUrl || 'Não configurada'}
                                        </span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-600 font-medium">API Key:</span>
                                        <span className="font-mono text-sm bg-white px-3 py-1 rounded-lg">
                                            {settings.apiKey ? `${settings.apiKey.substring(0, 8)}...` : 'Não configurada'}
                                        </span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-600 font-medium">Status:</span>
                                        <Badge variant={settings.baseUrl && settings.apiKey ? 'success' : 'error'}>
                                            {settings.baseUrl && settings.apiKey ? '✅ Configurada' : '❌ Pendente'}
                                        </Badge>
                                    </div>
                                </div>
                            </div>

                            <Button
                                onClick={handleTest}
                                disabled={!settings.baseUrl || !settings.apiKey}
                                loading={loading}
                                variant="primary"
                                size="lg"
                                fullWidth
                                icon="🔍"
                            >
                                Testar Conexão
                            </Button>

                            {result && (
                                <div className={`
                                    p-6 rounded-xl border-l-4
                                    ${result.success
                                        ? 'bg-green-50 border-green-500'
                                        : 'bg-red-50 border-red-500'
                                    }
                                `}>
                                    <div className="flex items-center gap-3 mb-4">
                                        <span className={`text-2xl ${result.success ? 'text-green-600' : 'text-red-600'}`}>
                                            {result.success ? '✅' : '❌'}
                                        </span>
                                        <span className={`font-bold text-lg ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                                            {result.success ? 'Conexão estabelecida com sucesso!' : 'Falha na conexão'}
                                        </span>
                                    </div>

                                    {result.error && (
                                        <p className="text-red-700 mb-4 font-medium">{result.error}</p>
                                    )}

                                    {result.data && (
                                        <details className="text-sm">
                                            <summary className="cursor-pointer text-gray-700 hover:text-gray-900 font-medium mb-2">
                                                📊 Ver resposta da API ({result.data.length} instância(s) encontrada(s))
                                            </summary>
                                            <pre className="mt-3 p-4 bg-white rounded-lg border text-xs overflow-auto max-h-60 font-mono">
                                                {JSON.stringify(result.data, null, 2)}
                                            </pre>
                                        </details>
                                    )}
                                </div>
                            )}

                            {!settings.baseUrl || !settings.apiKey ? (
                                <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                                    <div className="flex items-center gap-3">
                                        <span className="text-yellow-600 text-2xl">⚠️</span>
                                        <p className="text-yellow-800 font-semibold">
                                            Configure a URL Base e API Key na aba "Configurações" antes de testar a conexão.
                                        </p>
                                    </div>
                                </div>
                            ) : null}
                        </div>
                    </Card>
                </div>
            );
        };

        // Componente de Instâncias Premium
        const Instances = () => {
            const [instances, setInstances] = useState(() => Storage.load('instances', []));
            const [newInstanceName, setNewInstanceName] = useState('');
            const [qrCode, setQrCode] = useState('');
            const [selectedInstance, setSelectedInstance] = useState('');

            const {
                fetchInstances,
                createInstance,
                getQRCode,
                getInstanceStatus,
                loading
            } = useAPI();
            const { addNotification } = useNotification();

            const loadInstances = async () => {
                try {
                    const fetchedInstances = await fetchInstances();
                    setInstances(fetchedInstances);
                } catch (error) {
                    console.error('Erro ao carregar instâncias:', error);
                }
            };

            const handleCreateInstance = async () => {
                if (!newInstanceName.trim()) {
                    addNotification('Digite um nome para a instância', 'error');
                    return;
                }

                try {
                    await createInstance(newInstanceName);
                    setNewInstanceName('');
                    await loadInstances();
                } catch (error) {
                    console.error('Erro ao criar instância:', error);
                }
            };

            const handleGetQRCode = async (instanceName) => {
                try {
                    setSelectedInstance(instanceName);
                    const qrImage = await getQRCode(instanceName);
                    setQrCode(qrImage);
                    addNotification('QR Code gerado! Escaneie com seu WhatsApp', 'success');
                } catch (error) {
                    console.error('Erro ao obter QR Code:', error);
                    setQrCode('');
                }
            };

            const handleCheckStatus = async (instanceName) => {
                try {
                    const status = await getInstanceStatus(instanceName);
                    addNotification(`Status da instância ${instanceName}: ${status.state || 'Desconhecido'}`, 'info');
                    await loadInstances();
                } catch (error) {
                    console.error('Erro ao verificar status:', error);
                }
            };

            useEffect(() => {
                loadInstances();
            }, []);

            return (
                <div className="space-y-6">
                    <Card gradient>
                        <div className="flex items-center justify-between mb-6">
                            <div className="flex items-center gap-4">
                                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl flex items-center justify-center">
                                    <span className="text-white text-2xl">🖥️</span>
                                </div>
                                <div>
                                    <h2 className="text-2xl font-bold text-gray-900">Gerenciar Instâncias</h2>
                                    <p className="text-gray-600">Configure suas instâncias do WhatsApp</p>
                                </div>
                            </div>
                            <Button onClick={loadInstances} loading={loading} variant="outline" icon="🔄">
                                Atualizar
                            </Button>
                        </div>

                        {/* Criar Nova Instância */}
                        <div className="bg-gradient-to-r from-gray-50 to-purple-50 rounded-2xl p-6 mb-6">
                            <h3 className="font-bold mb-4 text-lg">➕ Criar Nova Instância</h3>
                            <div className="flex gap-4">
                                <Input
                                    placeholder="Nome da instância (ex: meu_whatsapp)"
                                    value={newInstanceName}
                                    onChange={(e) => setNewInstanceName(e.target.value)}
                                    className="flex-1"
                                    icon="📱"
                                />
                                <Button
                                    onClick={handleCreateInstance}
                                    loading={loading}
                                    disabled={!newInstanceName.trim()}
                                    variant="primary"
                                    icon="➕"
                                >
                                    Criar
                                </Button>
                            </div>
                        </div>

                        {/* Lista de Instâncias */}
                        <div className="space-y-4">
                            {instances.length > 0 ? instances.map(instance => (
                                <div key={instance.id} className="bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-4">
                                            <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                                                instance.status === 'open'
                                                    ? 'bg-green-100'
                                                    : 'bg-red-100'
                                            }`}>
                                                <span className="text-2xl">
                                                    {instance.status === 'open' ? '📱' : '📵'}
                                                </span>
                                            </div>
                                            <div>
                                                <h4 className="font-bold text-gray-900 text-lg">{instance.name}</h4>
                                                <p className="text-gray-600">
                                                    {instance.profileName || 'Aguardando conexão...'}
                                                </p>
                                                <Badge variant={instance.status === 'open' ? 'success' : 'error'} size="sm">
                                                    {instance.status === 'open' ? '🟢 Conectado' : '🔴 Desconectado'}
                                                </Badge>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <Button
                                                onClick={() => handleGetQRCode(instance.name)}
                                                variant="outline"
                                                size="sm"
                                                loading={loading && selectedInstance === instance.name}
                                                icon="📱"
                                            >
                                                QR Code
                                            </Button>
                                            <Button
                                                onClick={() => handleCheckStatus(instance.name)}
                                                variant="outline"
                                                size="sm"
                                                loading={loading}
                                                icon="🔍"
                                            >
                                                Status
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            )) : (
                                <div className="text-center py-12 text-gray-500">
                                    <div className="text-6xl mb-4">🖥️</div>
                                    <h3 className="text-lg font-semibold mb-2">Nenhuma instância encontrada</h3>
                                    <p className="text-sm">Crie uma nova instância acima ou verifique sua configuração da API</p>
                                </div>
                            )}
                        </div>
                    </Card>

                    {/* QR Code Modal */}
                    {qrCode && (
                        <Modal isOpen={true} onClose={() => setQrCode('')} title="📱 Conectar WhatsApp" size="md">
                            <div className="text-center space-y-6">
                                <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6">
                                    <img src={qrCode} alt="QR Code" className="w-64 h-64 mx-auto border-4 border-white rounded-2xl shadow-lg" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-bold text-gray-900 mb-2">Como conectar:</h3>
                                    <ol className="text-sm text-gray-600 space-y-1 text-left">
                                        <li>1. Abra o WhatsApp no seu celular</li>
                                        <li>2. Toque em "Mais opções" ou "Configurações"</li>
                                        <li>3. Toque em "Aparelhos conectados"</li>
                                        <li>4. Toque em "Conectar um aparelho"</li>
                                        <li>5. Aponte a câmera para este código QR</li>
                                    </ol>
                                </div>
                                <Button onClick={() => setQrCode('')} variant="primary" fullWidth>
                                    Fechar
                                </Button>
                            </div>
                        </Modal>
                    )}
                </div>
            );
        };

        // Componente de Dashboard Premium
        const Dashboard = () => {
            const contacts = Storage.load('contacts', []);
            const instances = Storage.load('instances', []);
            const settings = Storage.load('settings', {});
            const stats = Storage.load('stats', { total: 0, success: 0, errors: 0, lastRun: null });

            const connectedInstances = instances.filter(i => i.status === 'open').length;
            const isConfigured = settings.baseUrl && settings.apiKey;
            const isReady = isConfigured && connectedInstances > 0 && contacts.length > 0;

            return (
                <div className="space-y-6">
                    {/* Header do Dashboard */}
                    <Card gradient className="text-center">
                        <div className="flex items-center justify-center gap-4 mb-4">
                            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center">
                                <span className="text-white text-3xl">📊</span>
                            </div>
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">WhatsApp Sender Premium</h1>
                                <p className="text-gray-600">Sistema completo de disparos em massa</p>
                            </div>
                        </div>
                        <Badge variant={isReady ? 'success' : 'warning'} size="lg">
                            {isReady ? '🚀 Sistema Pronto' : '⚙️ Configuração Necessária'}
                        </Badge>
                    </Card>

                    {/* Cards de Estatísticas */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <Card className="text-center bg-gradient-to-br from-blue-50 to-blue-100">
                            <div className="text-4xl mb-3">🖥️</div>
                            <h3 className="text-lg font-bold text-gray-800">Instâncias</h3>
                            <p className="text-3xl font-bold text-blue-600">{connectedInstances}/{instances.length}</p>
                            <p className="text-sm text-gray-600">Conectadas</p>
                        </Card>

                        <Card className="text-center bg-gradient-to-br from-green-50 to-green-100">
                            <div className="text-4xl mb-3">👥</div>
                            <h3 className="text-lg font-bold text-gray-800">Contatos</h3>
                            <p className="text-3xl font-bold text-green-600">{contacts.length}</p>
                            <p className="text-sm text-gray-600">Cadastrados</p>
                        </Card>

                        <Card className="text-center bg-gradient-to-br from-purple-50 to-purple-100">
                            <div className="text-4xl mb-3">⚙️</div>
                            <h3 className="text-lg font-bold text-gray-800">API</h3>
                            <p className="text-3xl font-bold text-purple-600">
                                {isConfigured ? '✅' : '❌'}
                            </p>
                            <p className="text-sm text-gray-600">
                                {isConfigured ? 'Configurada' : 'Pendente'}
                            </p>
                        </Card>

                        <Card className="text-center bg-gradient-to-br from-yellow-50 to-yellow-100">
                            <div className="text-4xl mb-3">🚀</div>
                            <h3 className="text-lg font-bold text-gray-800">Status</h3>
                            <p className="text-3xl font-bold text-yellow-600">
                                {isReady ? '✅' : '⚙️'}
                            </p>
                            <p className="text-sm text-gray-600">
                                {isReady ? 'Pronto' : 'Configurar'}
                            </p>
                        </Card>
                    </div>

                    {/* Últimas Estatísticas */}
                    {stats.lastRun && (
                        <Card gradient>
                            <h2 className="text-xl font-bold mb-4 flex items-center gap-2">
                                <span>📈</span> Última Campanha
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                                <div className="text-center bg-white/50 rounded-xl p-4">
                                    <p className="text-3xl font-bold text-blue-600">{stats.total}</p>
                                    <p className="text-sm text-gray-600 font-semibold">Total</p>
                                </div>
                                <div className="text-center bg-white/50 rounded-xl p-4">
                                    <p className="text-3xl font-bold text-green-600">{stats.success}</p>
                                    <p className="text-sm text-gray-600 font-semibold">Sucessos</p>
                                </div>
                                <div className="text-center bg-white/50 rounded-xl p-4">
                                    <p className="text-3xl font-bold text-red-600">{stats.errors}</p>
                                    <p className="text-sm text-gray-600 font-semibold">Erros</p>
                                </div>
                                <div className="text-center bg-white/50 rounded-xl p-4">
                                    <p className="text-sm font-semibold text-gray-700">Executada em</p>
                                    <p className="text-sm text-gray-600">{stats.lastRun}</p>
                                </div>
                            </div>
                        </Card>
                    )}

                    {/* Primeiros Passos ou Ações Rápidas */}
                    {!isReady ? (
                        <Card className="bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
                            <h2 className="text-xl font-bold mb-4 text-yellow-800 flex items-center gap-2">
                                <span>🎯</span> Primeiros Passos
                            </h2>
                            <div className="space-y-3 text-sm text-yellow-700">
                                <div className="flex items-center gap-3">
                                    {isConfigured ? '✅' : '1️⃣'}
                                    <span className={isConfigured ? 'line-through' : 'font-semibold'}>
                                        Configure sua Evolution API na aba "Configurações"
                                    </span>
                                </div>
                                <div className="flex items-center gap-3">
                                    {connectedInstances > 0 ? '✅' : '2️⃣'}
                                    <span className={connectedInstances > 0 ? 'line-through' : 'font-semibold'}>
                                        Configure instâncias na aba "Instâncias"
                                    </span>
                                </div>
                                <div className="flex items-center gap-3">
                                    {contacts.length > 0 ? '✅' : '3️⃣'}
                                    <span className={contacts.length > 0 ? 'line-through' : 'font-semibold'}>
                                        Adicione contatos na aba "Contatos"
                                    </span>
                                </div>
                                <div className="flex items-center gap-3">
                                    {isReady ? '✅' : '4️⃣'}
                                    <span className={isReady ? 'line-through' : 'font-semibold'}>
                                        Execute campanhas na aba "Disparador"
                                    </span>
                                </div>
                            </div>
                        </Card>
                    ) : (
                        <Card gradient>
                            <h2 className="text-xl font-bold mb-4 flex items-center gap-2">
                                <span>🚀</span> Sistema Pronto - Ações Rápidas
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <Button
                                    onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'campaign' }))}
                                    variant="primary"
                                    size="lg"
                                    fullWidth
                                    icon="📱"
                                >
                                    Nova Campanha
                                </Button>
                                <Button
                                    onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'contacts' }))}
                                    variant="success"
                                    size="lg"
                                    fullWidth
                                    icon="👥"
                                >
                                    Gerenciar Contatos
                                </Button>
                                <Button
                                    onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'instances' }))}
                                    variant="outline"
                                    size="lg"
                                    fullWidth
                                    icon="🖥️"
                                >
                                    Ver Instâncias
                                </Button>
                            </div>
                        </Card>
                    )}
                </div>
            );
        };

        // Componente Principal da Aplicação Premium
        const App = () => {
            const [currentView, setCurrentView] = useState('dashboard');
            const { notifications, addNotification, removeNotification } = useNotification();

            const views = {
                dashboard: { name: 'Dashboard', icon: '📊', component: Dashboard },
                campaign: { name: 'Disparador', icon: '🚀', component: Campaign },
                contacts: { name: 'Contatos', icon: '👥', component: Contacts },
                instances: { name: 'Instâncias', icon: '🖥️', component: Instances },
                settings: { name: 'Configurações', icon: '⚙️', component: Settings },
                test: { name: 'Teste API', icon: '🔍', component: ApiTest }
            };

            const CurrentComponent = views[currentView]?.component || Dashboard;

            // Listener para navegação via eventos
            useEffect(() => {
                const handleNavigate = (event) => {
                    setCurrentView(event.detail);
                };
                window.addEventListener('navigate', handleNavigate);
                return () => window.removeEventListener('navigate', handleNavigate);
            }, []);

            return (
                <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
                    {/* Header Premium */}
                    <header className="bg-white/80 backdrop-blur-lg shadow-lg border-b border-gray-200/50 sticky top-0 z-40">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="flex justify-between items-center h-20">
                                <div className="flex items-center gap-4">
                                    <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-700 rounded-2xl flex items-center justify-center shadow-lg">
                                        <span className="text-white font-bold text-lg">WS</span>
                                    </div>
                                    <div>
                                        <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                            WhatsApp Sender
                                        </h1>
                                        <p className="text-sm text-gray-600">Sistema Premium v3.0</p>
                                    </div>
                                    <Badge variant="success" size="lg">
                                        🚀 100% Funcional
                                    </Badge>
                                </div>
                                <div className="text-sm text-gray-600 bg-white/50 px-4 py-2 rounded-xl">
                                    ✨ Sistema Completo e Otimizado
                                </div>
                            </div>
                        </div>
                    </header>

                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                        {/* Navigation Premium */}
                        <div className="mb-8">
                            <nav className="flex flex-wrap gap-2 bg-white/60 backdrop-blur-lg p-2 rounded-2xl shadow-lg border border-gray-200/50">
                                {Object.entries(views).map(([key, view]) => (
                                    <button
                                        key={key}
                                        onClick={() => setCurrentView(key)}
                                        className={`
                                            px-6 py-3 rounded-xl text-sm font-semibold transition-all duration-300 flex items-center gap-3
                                            transform hover:scale-105 active:scale-95
                                            ${currentView === key
                                                ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                                                : 'text-gray-600 hover:text-gray-900 hover:bg-white/80'
                                            }
                                        `}
                                    >
                                        <span className="text-lg">{view.icon}</span>
                                        <span className="hidden sm:inline">{view.name}</span>
                                    </button>
                                ))}
                            </nav>
                        </div>

                        {/* Content */}
                        <main className="animate-fade-in">
                            <CurrentComponent />
                        </main>
                    </div>

                    {/* Footer Premium */}
                    <footer className="bg-white/80 backdrop-blur-lg border-t border-gray-200/50 mt-16">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                            <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
                                <div className="text-center lg:text-left">
                                    <p className="text-lg font-semibold text-gray-800">
                                        WhatsApp Sender Premium v3.0
                                    </p>
                                    <p className="text-sm text-gray-600">
                                        Sistema completo de disparos em massa com importação CSV e campos dinâmicos
                                    </p>
                                </div>
                                <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600">
                                    <div className="flex items-center gap-2">
                                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                                        <span>React 18</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                                        <span>Tailwind CSS</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                                        <span>Evolution API</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                                        <span>CSV Import</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                                        <span>Campos Dinâmicos</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </footer>

                    {/* Notificações Premium */}
                    <NotificationContainer
                        notifications={notifications}
                        removeNotification={removeNotification}
                    />
                </div>
            );
        };

        // Renderizar a aplicação
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
