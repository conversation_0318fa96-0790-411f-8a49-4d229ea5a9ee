// Performance monitoring and optimization utilities
import React from 'react';

// Measure function execution time
export const measurePerformance = <T extends (...args: any[]) => any>(
  fn: T,
  label?: string
): T => {
  return ((...args: Parameters<T>) => {
    const start = performance.now();
    const result = fn(...args);
    const end = performance.now();
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${label || fn.name} took ${(end - start).toFixed(2)}ms`);
    }
    
    return result;
  }) as T;
};

// Measure async function execution time
export const measureAsyncPerformance = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  label?: string
): T => {
  return (async (...args: Parameters<T>) => {
    const start = performance.now();
    const result = await fn(...args);
    const end = performance.now();
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${label || fn.name} took ${(end - start).toFixed(2)}ms`);
    }
    
    return result;
  }) as T;
};

// Memory usage monitoring
export const getMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    return {
      used: Math.round(memory.usedJSHeapSize / 1048576), // MB
      total: Math.round(memory.totalJSHeapSize / 1048576), // MB
      limit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
    };
  }
  return null;
};

// Log memory usage
export const logMemoryUsage = (label?: string) => {
  const memory = getMemoryUsage();
  if (memory && process.env.NODE_ENV === 'development') {
    console.log(`${label || 'Memory usage'}:`, memory);
  }
};

// Bundle size analyzer helper
export const analyzeBundle = () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('Bundle analysis available in development mode');
    // In a real app, you might integrate with webpack-bundle-analyzer
  }
};

// Lazy loading helper for components
export const createLazyComponent = <T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) => {
  const LazyComponent = React.lazy(importFn);
  
  return (props: React.ComponentProps<T>) => (
    <React.Suspense 
      fallback={
        fallback ? React.createElement(fallback) : (
          <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        )
      }
    >
      <LazyComponent {...props} />
    </React.Suspense>
  );
};

// Preload resources
export const preloadResource = (url: string, type: 'image' | 'script' | 'style' = 'image') => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = url;
  
  switch (type) {
    case 'image':
      link.as = 'image';
      break;
    case 'script':
      link.as = 'script';
      break;
    case 'style':
      link.as = 'style';
      break;
  }
  
  document.head.appendChild(link);
};

// Batch DOM updates
export const batchDOMUpdates = (updates: (() => void)[]) => {
  requestAnimationFrame(() => {
    updates.forEach(update => update());
  });
};

// Optimize images for web
export const optimizeImageUrl = (
  url: string, 
  width?: number, 
  height?: number, 
  quality: number = 80
): string => {
  // This would integrate with an image optimization service
  // For now, just return the original URL
  return url;
};

// Check if user prefers reduced motion
export const prefersReducedMotion = (): boolean => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// Adaptive loading based on connection
export const getConnectionQuality = (): 'slow' | 'fast' | 'unknown' => {
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    const effectiveType = connection.effectiveType;
    
    if (effectiveType === 'slow-2g' || effectiveType === '2g') {
      return 'slow';
    } else if (effectiveType === '3g' || effectiveType === '4g') {
      return 'fast';
    }
  }
  
  return 'unknown';
};

// Performance observer for monitoring
export const observePerformance = () => {
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (process.env.NODE_ENV === 'development') {
          console.log(`Performance entry: ${entry.name} - ${entry.duration}ms`);
        }
      });
    });
    
    observer.observe({ entryTypes: ['measure', 'navigation', 'resource'] });
    
    return () => observer.disconnect();
  }
  
  return () => {};
};
