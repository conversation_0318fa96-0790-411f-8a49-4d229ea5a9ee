import { useRef, useCallback, useEffect } from 'react';
import * as Comlink from 'comlink';
import type { CampaignWorkerAPI } from '@/workers/campaignWorker';

interface WorkerState {
  isLoading: boolean;
  error: Error | null;
  progress: number;
}

// Hook for using Web Workers with Comlink
export const useWorker = () => {
  const workerRef = useRef<Worker | null>(null);
  const apiRef = useRef<Comlink.Remote<CampaignWorkerAPI> | null>(null);
  const [state, setState] = useState<WorkerState>({
    isLoading: false,
    error: null,
    progress: 0,
  });

  // Initialize worker
  const initWorker = useCallback(async () => {
    if (typeof window === 'undefined') return;

    try {
      // Create worker from URL
      const workerUrl = new URL('../workers/campaignWorker.ts', import.meta.url);
      workerRef.current = new Worker(workerUrl, { type: 'module' });
      
      // Wrap with Comlink
      apiRef.current = Comlink.wrap<CampaignWorkerAPI>(workerRef.current);
    } catch (error) {
      console.error('Failed to initialize worker:', error);
      setState(prev => ({ ...prev, error: error as Error }));
    }
  }, []);

  // Terminate worker
  const terminateWorker = useCallback(() => {
    if (workerRef.current) {
      workerRef.current.terminate();
      workerRef.current = null;
      apiRef.current = null;
    }
  }, []);

  // Execute worker function with loading state
  const execute = useCallback(async <T>(
    fn: (api: Comlink.Remote<CampaignWorkerAPI>) => Promise<T>
  ): Promise<T | null> => {
    if (!apiRef.current) {
      await initWorker();
      if (!apiRef.current) {
        throw new Error('Failed to initialize worker');
      }
    }

    setState(prev => ({ ...prev, isLoading: true, error: null, progress: 0 }));

    try {
      const result = await fn(apiRef.current);
      setState(prev => ({ ...prev, isLoading: false, progress: 100 }));
      return result;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: error as Error,
        progress: 0 
      }));
      throw error;
    }
  }, [initWorker]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      terminateWorker();
    };
  }, [terminateWorker]);

  return {
    ...state,
    execute,
    initWorker,
    terminateWorker,
    isReady: !!apiRef.current,
  };
};

// Specific hooks for different worker operations
export const useCampaignProcessor = () => {
  const { execute, ...workerState } = useWorker();

  const processCampaign = useCallback(async (config: any) => {
    return execute(api => api.processCampaign(config));
  }, [execute]);

  const processContacts = useCallback(async (contacts: any[], filters: any) => {
    return execute(api => api.processContacts(contacts, filters));
  }, [execute]);

  const generateMessages = useCallback(async (template: string, contacts: any[]) => {
    return execute(api => api.generateMessages(template, contacts));
  }, [execute]);

  const validatePhoneNumbers = useCallback(async (phones: string[]) => {
    return execute(api => api.validatePhoneNumbers(phones));
  }, [execute]);

  const optimizeImages = useCallback(async (images: any[]) => {
    return execute(api => api.optimizeImages(images));
  }, [execute]);

  const parseCSV = useCallback(async (csvData: string) => {
    return execute(api => api.parseCSV(csvData));
  }, [execute]);

  return {
    ...workerState,
    processCampaign,
    processContacts,
    generateMessages,
    validatePhoneNumbers,
    optimizeImages,
    parseCSV,
  };
};

// Hook for batch processing with progress tracking
export const useBatchProcessor = <T, R>(
  processor: (item: T) => Promise<R>,
  batchSize: number = 10
) => {
  const [state, setState] = useState({
    isProcessing: false,
    progress: 0,
    completed: 0,
    total: 0,
    results: [] as R[],
    errors: [] as Error[],
  });

  const processBatch = useCallback(async (items: T[]): Promise<R[]> => {
    setState({
      isProcessing: true,
      progress: 0,
      completed: 0,
      total: items.length,
      results: [],
      errors: [],
    });

    const results: R[] = [];
    const errors: Error[] = [];

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      
      try {
        const batchResults = await Promise.allSettled(
          batch.map(item => processor(item))
        );

        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            results.push(result.value);
          } else {
            errors.push(new Error(`Item ${i + index}: ${result.reason}`));
          }
        });

        const completed = Math.min(i + batchSize, items.length);
        const progress = (completed / items.length) * 100;

        setState(prev => ({
          ...prev,
          completed,
          progress,
          results: [...results],
          errors: [...errors],
        }));

        // Small delay to prevent blocking
        await new Promise(resolve => setTimeout(resolve, 10));
      } catch (error) {
        errors.push(error as Error);
      }
    }

    setState(prev => ({
      ...prev,
      isProcessing: false,
      progress: 100,
    }));

    return results;
  }, [processor, batchSize]);

  const reset = useCallback(() => {
    setState({
      isProcessing: false,
      progress: 0,
      completed: 0,
      total: 0,
      results: [],
      errors: [],
    });
  }, []);

  return {
    ...state,
    processBatch,
    reset,
  };
};

// Hook for file processing with Web Workers
export const useFileProcessor = () => {
  const { execute, ...workerState } = useWorker();

  const processFile = useCallback(async (file: File, type: 'csv' | 'image' | 'media') => {
    const arrayBuffer = await file.arrayBuffer();

    switch (type) {
      case 'csv':
        const text = new TextDecoder().decode(arrayBuffer);
        return execute(api => api.parseCSV(text));

      case 'image':
        return execute(api => api.optimizeImages([{
          data: arrayBuffer,
          type: file.type,
          name: file.name,
        }]));

      default:
        throw new Error(`Unsupported file type: ${type}`);
    }
  }, [execute]);

  const processFiles = useCallback(async (files: File[], type: 'csv' | 'image' | 'media') => {
    const results = [];
    
    for (const file of files) {
      try {
        const result = await processFile(file, type);
        results.push({ file: file.name, result, success: true });
      } catch (error) {
        results.push({ file: file.name, error, success: false });
      }
    }

    return results;
  }, [processFile]);

  return {
    ...workerState,
    processFile,
    processFiles,
  };
};

// Performance monitoring hook
export const usePerformanceWorker = () => {
  const [metrics, setMetrics] = useState({
    memoryUsage: 0,
    renderTime: 0,
    bundleSize: 0,
    networkRequests: 0,
  });

  const measurePerformance = useCallback(async (operation: () => Promise<any>) => {
    const startTime = performance.now();
    const startMemory = (performance as any).memory?.usedJSHeapSize || 0;

    try {
      const result = await operation();
      
      const endTime = performance.now();
      const endMemory = (performance as any).memory?.usedJSHeapSize || 0;

      setMetrics(prev => ({
        ...prev,
        renderTime: endTime - startTime,
        memoryUsage: endMemory - startMemory,
      }));

      return result;
    } catch (error) {
      const endTime = performance.now();
      setMetrics(prev => ({
        ...prev,
        renderTime: endTime - startTime,
      }));
      throw error;
    }
  }, []);

  const getNetworkMetrics = useCallback(() => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
      };
    }
    return null;
  }, []);

  return {
    metrics,
    measurePerformance,
    getNetworkMetrics,
  };
};
