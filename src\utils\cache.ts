import { openDB, DBSchema, IDBPDatabase } from 'idb';

// IndexedDB Schema
interface CacheDB extends DBSchema {
  cache: {
    key: string;
    value: {
      data: any;
      timestamp: number;
      ttl: number;
      tags: string[];
    };
  };
  files: {
    key: string;
    value: {
      data: ArrayBuffer;
      type: string;
      timestamp: number;
      size: number;
    };
  };
}

class CacheManager {
  private db: IDBPDatabase<CacheDB> | null = null;
  private memoryCache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private maxMemorySize = 100; // Maximum items in memory cache

  async init() {
    if (typeof window === 'undefined') return;
    
    try {
      this.db = await openDB<CacheDB>('whatsapp-sender-cache', 1, {
        upgrade(db) {
          // Create cache store
          const cacheStore = db.createObjectStore('cache', { keyPath: 'key' });
          cacheStore.createIndex('timestamp', 'timestamp');
          cacheStore.createIndex('tags', 'tags', { multiEntry: true });

          // Create files store
          const filesStore = db.createObjectStore('files', { keyPath: 'key' });
          filesStore.createIndex('timestamp', 'timestamp');
        },
      });
    } catch (error) {
      console.error('Failed to initialize cache database:', error);
    }
  }

  // Memory cache operations
  setMemory(key: string, data: any, ttl: number = 300000) { // 5 minutes default
    // Clean old entries if cache is full
    if (this.memoryCache.size >= this.maxMemorySize) {
      const oldestKey = Array.from(this.memoryCache.keys())[0];
      this.memoryCache.delete(oldestKey);
    }

    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  getMemory(key: string): any | null {
    const cached = this.memoryCache.get(key);
    if (!cached) return null;

    // Check if expired
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.memoryCache.delete(key);
      return null;
    }

    return cached.data;
  }

  deleteMemory(key: string) {
    this.memoryCache.delete(key);
  }

  clearMemory() {
    this.memoryCache.clear();
  }

  // IndexedDB cache operations
  async set(key: string, data: any, ttl: number = 3600000, tags: string[] = []) { // 1 hour default
    // Set in memory cache first for fast access
    this.setMemory(key, data, ttl);

    if (!this.db) return;

    try {
      await this.db.put('cache', {
        key,
        data,
        timestamp: Date.now(),
        ttl,
        tags,
      });
    } catch (error) {
      console.error('Failed to set cache:', error);
    }
  }

  async get(key: string): Promise<any | null> {
    // Try memory cache first
    const memoryResult = this.getMemory(key);
    if (memoryResult !== null) return memoryResult;

    if (!this.db) return null;

    try {
      const cached = await this.db.get('cache', key);
      if (!cached) return null;

      // Check if expired
      if (Date.now() - cached.timestamp > cached.ttl) {
        await this.delete(key);
        return null;
      }

      // Update memory cache
      this.setMemory(key, cached.data, cached.ttl - (Date.now() - cached.timestamp));

      return cached.data;
    } catch (error) {
      console.error('Failed to get cache:', error);
      return null;
    }
  }

  async delete(key: string) {
    this.deleteMemory(key);

    if (!this.db) return;

    try {
      await this.db.delete('cache', key);
    } catch (error) {
      console.error('Failed to delete cache:', error);
    }
  }

  async clear() {
    this.clearMemory();

    if (!this.db) return;

    try {
      await this.db.clear('cache');
      await this.db.clear('files');
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }

  async invalidateByTags(tags: string[]) {
    if (!this.db) return;

    try {
      const tx = this.db.transaction('cache', 'readwrite');
      const store = tx.objectStore('cache');
      const index = store.index('tags');

      for (const tag of tags) {
        const keys = await index.getAllKeys(tag);
        for (const key of keys) {
          await store.delete(key);
          this.deleteMemory(key as string);
        }
      }

      await tx.done;
    } catch (error) {
      console.error('Failed to invalidate cache by tags:', error);
    }
  }

  // File cache operations
  async setFile(key: string, file: File | ArrayBuffer, type?: string) {
    if (!this.db) return;

    try {
      const data = file instanceof File ? await file.arrayBuffer() : file;
      const fileType = type || (file instanceof File ? file.type : 'application/octet-stream');

      await this.db.put('files', {
        key,
        data,
        type: fileType,
        timestamp: Date.now(),
        size: data.byteLength,
      });
    } catch (error) {
      console.error('Failed to cache file:', error);
    }
  }

  async getFile(key: string): Promise<{ data: ArrayBuffer; type: string } | null> {
    if (!this.db) return null;

    try {
      const cached = await this.db.get('files', key);
      if (!cached) return null;

      return {
        data: cached.data,
        type: cached.type,
      };
    } catch (error) {
      console.error('Failed to get cached file:', error);
      return null;
    }
  }

  async deleteFile(key: string) {
    if (!this.db) return;

    try {
      await this.db.delete('files', key);
    } catch (error) {
      console.error('Failed to delete cached file:', error);
    }
  }

  // Cleanup expired entries
  async cleanup() {
    if (!this.db) return;

    try {
      const now = Date.now();
      const tx = this.db.transaction(['cache', 'files'], 'readwrite');

      // Clean cache store
      const cacheStore = tx.objectStore('cache');
      const cacheIndex = cacheStore.index('timestamp');
      const cacheCursor = await cacheIndex.openCursor();
      
      if (cacheCursor) {
        do {
          const { value } = cacheCursor;
          if (now - value.timestamp > value.ttl) {
            await cacheCursor.delete();
            this.deleteMemory(value.key);
          }
        } while (await cacheCursor.continue());
      }

      // Clean files store (keep for 7 days)
      const filesStore = tx.objectStore('files');
      const filesIndex = filesStore.index('timestamp');
      const filesCursor = await filesIndex.openCursor();
      const maxFileAge = 7 * 24 * 60 * 60 * 1000; // 7 days
      
      if (filesCursor) {
        do {
          const { value } = filesCursor;
          if (now - value.timestamp > maxFileAge) {
            await filesCursor.delete();
          }
        } while (await filesCursor.continue());
      }

      await tx.done;
    } catch (error) {
      console.error('Failed to cleanup cache:', error);
    }
  }

  // Get cache statistics
  async getStats() {
    if (!this.db) return { cache: 0, files: 0, memorySize: this.memoryCache.size };

    try {
      const cacheCount = await this.db.count('cache');
      const filesCount = await this.db.count('files');

      return {
        cache: cacheCount,
        files: filesCount,
        memorySize: this.memoryCache.size,
      };
    } catch (error) {
      console.error('Failed to get cache stats:', error);
      return { cache: 0, files: 0, memorySize: this.memoryCache.size };
    }
  }
}

// Create singleton instance
export const cacheManager = new CacheManager();

// Initialize cache on module load
if (typeof window !== 'undefined') {
  cacheManager.init();
  
  // Cleanup every hour
  setInterval(() => {
    cacheManager.cleanup();
  }, 60 * 60 * 1000);
}

// Cache decorator for functions
export function cached(ttl: number = 300000, tags: string[] = []) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const cacheKey = `${target.constructor.name}.${propertyName}:${JSON.stringify(args)}`;
      
      // Try to get from cache
      const cached = await cacheManager.get(cacheKey);
      if (cached !== null) {
        return cached;
      }

      // Execute method and cache result
      const result = await method.apply(this, args);
      await cacheManager.set(cacheKey, result, ttl, tags);
      
      return result;
    };
  };
}

// React hook for cached data
export function useCachedData<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttl: number = 300000,
  tags: string[] = []
) {
  const [data, setData] = React.useState<T | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    let mounted = true;

    async function loadData() {
      try {
        setLoading(true);
        setError(null);

        // Try cache first
        const cached = await cacheManager.get(key);
        if (cached !== null && mounted) {
          setData(cached);
          setLoading(false);
          return;
        }

        // Fetch fresh data
        const result = await fetcher();
        if (mounted) {
          setData(result);
          await cacheManager.set(key, result, ttl, tags);
        }
      } catch (err) {
        if (mounted) {
          setError(err instanceof Error ? err : new Error('Unknown error'));
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    }

    loadData();

    return () => {
      mounted = false;
    };
  }, [key, ttl]);

  const invalidate = React.useCallback(async () => {
    await cacheManager.delete(key);
  }, [key]);

  return { data, loading, error, invalidate };
}
