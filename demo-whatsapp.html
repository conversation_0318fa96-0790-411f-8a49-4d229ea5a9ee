<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disparador WhatsApp - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="text-center mb-8 animate-fade-in">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">
                📱 Disparador WhatsApp Refatorado
            </h1>
            <p class="text-gray-600 text-lg">
                Demonstração das melhorias implementadas
            </p>
        </header>

        <!-- Status Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-lg p-6 card-hover transition-all duration-300">
                <div class="flex items-center">
                    <div class="bg-blue-100 p-3 rounded-full">
                        <span class="text-2xl">🖥️</span>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800">Instâncias</h3>
                        <p class="text-2xl font-bold text-blue-600">2/3</p>
                        <p class="text-sm text-gray-500">Conectadas</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6 card-hover transition-all duration-300">
                <div class="flex items-center">
                    <div class="bg-green-100 p-3 rounded-full">
                        <span class="text-2xl">👥</span>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800">Contatos</h3>
                        <p class="text-2xl font-bold text-green-600">1,234</p>
                        <p class="text-sm text-gray-500">Na base</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6 card-hover transition-all duration-300">
                <div class="flex items-center">
                    <div class="bg-purple-100 p-3 rounded-full">
                        <span class="text-2xl">💬</span>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800">Mensagens</h3>
                        <p class="text-2xl font-bold text-purple-600">5,678</p>
                        <p class="text-sm text-gray-500">Enviadas</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6 card-hover transition-all duration-300">
                <div class="flex items-center">
                    <div class="bg-emerald-100 p-3 rounded-full">
                        <span class="text-2xl">✅</span>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800">Sucesso</h3>
                        <p class="text-2xl font-bold text-emerald-600">98.5%</p>
                        <p class="text-sm text-gray-500">Taxa</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">🧭 Navegação</h2>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <button onclick="showView('dashboard')" class="nav-btn bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-lg transition-colors">
                    📊 Dashboard
                </button>
                <button onclick="showView('disparador')" class="nav-btn bg-green-500 hover:bg-green-600 text-white p-3 rounded-lg transition-colors">
                    🚀 Disparador
                </button>
                <button onclick="showView('contatos')" class="nav-btn bg-purple-500 hover:bg-purple-600 text-white p-3 rounded-lg transition-colors">
                    👥 Contatos
                </button>
                <button onclick="showView('instancias')" class="nav-btn bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-lg transition-colors">
                    🖥️ Instâncias
                </button>
                <button onclick="showView('templates')" class="nav-btn bg-pink-500 hover:bg-pink-600 text-white p-3 rounded-lg transition-colors">
                    📝 Templates
                </button>
                <button onclick="showView('settings')" class="nav-btn bg-gray-500 hover:bg-gray-600 text-white p-3 rounded-lg transition-colors">
                    ⚙️ Settings
                </button>
            </div>
        </div>

        <!-- Content Area -->
        <div id="content-area" class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div id="dashboard-view">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">📊 Dashboard</h2>
                <p class="text-gray-600 mb-4">Bem-vindo ao sistema refatorado! Principais melhorias:</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-gray-800 mb-2">🏗️ Arquitetura</h3>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>✅ Código modular (35+ arquivos)</li>
                            <li>✅ Componentes reutilizáveis</li>
                            <li>✅ Hooks personalizados</li>
                            <li>✅ Context API</li>
                        </ul>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-gray-800 mb-2">🎨 Design</h3>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>✅ Design system consistente</li>
                            <li>✅ Responsivo mobile-first</li>
                            <li>✅ Animações suaves</li>
                            <li>✅ Tipografia profissional</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Demo Buttons -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">🧪 Teste as Funcionalidades</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="showNotification('success')" class="bg-green-500 hover:bg-green-600 text-white p-3 rounded-lg transition-colors">
                    ✅ Notificação de Sucesso
                </button>
                <button onclick="showNotification('error')" class="bg-red-500 hover:bg-red-600 text-white p-3 rounded-lg transition-colors">
                    ❌ Notificação de Erro
                </button>
                <button onclick="showNotification('info')" class="bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-lg transition-colors">
                    ℹ️ Notificação de Info
                </button>
            </div>
        </div>

        <!-- Improvements -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg p-6 text-white">
            <h2 class="text-2xl font-semibold mb-4">🚀 Melhorias Implementadas</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <h3 class="font-semibold mb-2">📈 Performance</h3>
                    <p class="text-sm opacity-90">Lazy loading, memoização, virtual scrolling</p>
                </div>
                <div>
                    <h3 class="font-semibold mb-2">🛡️ Confiabilidade</h3>
                    <p class="text-sm opacity-90">Error boundaries, retry mechanism</p>
                </div>
                <div>
                    <h3 class="font-semibold mb-2">🔧 Manutenibilidade</h3>
                    <p class="text-sm opacity-90">Código modular, componentes reutilizáveis</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notification-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <script>
        // Função para mostrar diferentes views
        function showView(viewName) {
            const contentArea = document.getElementById('content-area');
            const views = {
                dashboard: `
                    <h2 class="text-2xl font-semibold text-gray-800 mb-4">📊 Dashboard</h2>
                    <p class="text-gray-600 mb-4">Visão geral do sistema refatorado</p>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-600">Sistema funcionando perfeitamente! ✅</p>
                    </div>
                `,
                disparador: `
                    <h2 class="text-2xl font-semibold text-gray-800 mb-4">🚀 Disparador</h2>
                    <p class="text-gray-600 mb-4">Central de envio de mensagens em massa</p>
                    <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                        <p class="text-sm text-green-700">Pronto para enviar mensagens! 📱</p>
                    </div>
                `,
                contatos: `
                    <h2 class="text-2xl font-semibold text-gray-800 mb-4">👥 Contatos</h2>
                    <p class="text-gray-600 mb-4">Gerenciamento de contatos e grupos</p>
                    <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                        <p class="text-sm text-purple-700">1,234 contatos na base de dados 📋</p>
                    </div>
                `,
                instancias: `
                    <h2 class="text-2xl font-semibold text-gray-800 mb-4">🖥️ Instâncias</h2>
                    <p class="text-gray-600 mb-4">Gerenciamento de instâncias WhatsApp</p>
                    <div class="bg-orange-50 p-4 rounded-lg border border-orange-200">
                        <p class="text-sm text-orange-700">2 de 3 instâncias conectadas 🔗</p>
                    </div>
                `,
                templates: `
                    <h2 class="text-2xl font-semibold text-gray-800 mb-4">📝 Templates</h2>
                    <p class="text-gray-600 mb-4">Modelos de mensagens reutilizáveis</p>
                    <div class="bg-pink-50 p-4 rounded-lg border border-pink-200">
                        <p class="text-sm text-pink-700">15 templates salvos 📄</p>
                    </div>
                `,
                settings: `
                    <h2 class="text-2xl font-semibold text-gray-800 mb-4">⚙️ Configurações</h2>
                    <p class="text-gray-600 mb-4">Configurações do sistema</p>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">URL da API</label>
                            <input type="text" class="w-full p-2 border border-gray-300 rounded-lg" placeholder="https://sua-api.com">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                            <input type="password" class="w-full p-2 border border-gray-300 rounded-lg" placeholder="Sua chave de API">
                        </div>
                        <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                            Salvar Configurações
                        </button>
                    </div>
                `
            };
            
            contentArea.innerHTML = views[viewName] || views.dashboard;
            
            // Update active button
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('ring-2', 'ring-white');
            });
            event.target.classList.add('ring-2', 'ring-white');
        }

        // Função para mostrar notificações
        function showNotification(type) {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');
            
            const styles = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                info: 'bg-blue-500'
            };
            
            const messages = {
                success: '✅ Operação realizada com sucesso!',
                error: '❌ Erro ao processar solicitação!',
                info: 'ℹ️ Informação importante!'
            };
            
            notification.className = `${styles[type]} text-white p-4 rounded-lg shadow-lg animate-fade-in max-w-sm`;
            notification.innerHTML = `
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium">${messages[type]}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        ✕
                    </button>
                </div>
            `;
            
            container.appendChild(notification);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Mostrar notificação de boas-vindas
        setTimeout(() => {
            showNotification('success');
        }, 1000);
    </script>
</body>
</html>
