import React, { memo, useMemo, useCallback } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { cn } from '@/utils/cn';

export interface Column<T> {
  key: keyof T | string;
  header: string;
  width?: number | string;
  minWidth?: number;
  maxWidth?: number;
  sortable?: boolean;
  render?: (value: any, row: T, index: number) => React.ReactNode;
  className?: string;
  headerClassName?: string;
}

export interface VirtualizedTableProps<T> {
  data: T[];
  columns: Column<T>[];
  height?: number;
  rowHeight?: number;
  className?: string;
  onRowClick?: (row: T, index: number) => void;
  onSort?: (key: string, direction: 'asc' | 'desc') => void;
  sortKey?: string;
  sortDirection?: 'asc' | 'desc';
  loading?: boolean;
  emptyMessage?: string;
  stickyHeader?: boolean;
  striped?: boolean;
  hoverable?: boolean;
  selectable?: boolean;
  selectedRows?: Set<string | number>;
  onSelectionChange?: (selectedRows: Set<string | number>) => void;
  getRowId?: (row: T, index: number) => string | number;
}

const VirtualizedTableComponent = <T extends Record<string, any>>({
  data,
  columns,
  height = 400,
  rowHeight = 48,
  className,
  onRowClick,
  onSort,
  sortKey,
  sortDirection,
  loading = false,
  emptyMessage = 'Nenhum dado encontrado',
  stickyHeader = true,
  striped = true,
  hoverable = true,
  selectable = false,
  selectedRows = new Set(),
  onSelectionChange,
  getRowId = (_, index) => index,
}: VirtualizedTableProps<T>) => {
  const parentRef = React.useRef<HTMLDivElement>(null);

  // Virtualization
  const rowVirtualizer = useVirtualizer({
    count: data.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => rowHeight,
    overscan: 5,
  });

  // Handle sorting
  const handleSort = useCallback((key: string) => {
    if (!onSort) return;
    
    const newDirection = sortKey === key && sortDirection === 'asc' ? 'desc' : 'asc';
    onSort(key, newDirection);
  }, [onSort, sortKey, sortDirection]);

  // Handle row selection
  const handleRowSelection = useCallback((rowId: string | number, checked: boolean) => {
    if (!onSelectionChange) return;
    
    const newSelection = new Set(selectedRows);
    if (checked) {
      newSelection.add(rowId);
    } else {
      newSelection.delete(rowId);
    }
    onSelectionChange(newSelection);
  }, [selectedRows, onSelectionChange]);

  // Handle select all
  const handleSelectAll = useCallback((checked: boolean) => {
    if (!onSelectionChange) return;
    
    if (checked) {
      const allIds = data.map((row, index) => getRowId(row, index));
      onSelectionChange(new Set(allIds));
    } else {
      onSelectionChange(new Set());
    }
  }, [data, getRowId, onSelectionChange]);

  // Calculate column widths
  const columnWidths = useMemo(() => {
    return columns.map(col => {
      if (typeof col.width === 'number') return `${col.width}px`;
      if (typeof col.width === 'string') return col.width;
      return 'auto';
    });
  }, [columns]);

  // Check if all rows are selected
  const isAllSelected = useMemo(() => {
    if (data.length === 0) return false;
    return data.every((row, index) => selectedRows.has(getRowId(row, index)));
  }, [data, selectedRows, getRowId]);

  // Check if some rows are selected
  const isSomeSelected = useMemo(() => {
    return selectedRows.size > 0 && !isAllSelected;
  }, [selectedRows.size, isAllSelected]);

  if (loading) {
    return (
      <div className={cn('border rounded-lg', className)}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Carregando...</span>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className={cn('border rounded-lg', className)}>
        <div className="flex items-center justify-center h-64 text-gray-500">
          {emptyMessage}
        </div>
      </div>
    );
  }

  return (
    <div className={cn('border rounded-lg overflow-hidden', className)}>
      {/* Header */}
      <div 
        className={cn(
          'grid border-b bg-gray-50',
          stickyHeader && 'sticky top-0 z-10'
        )}
        style={{ gridTemplateColumns: columnWidths.join(' ') }}
      >
        {selectable && (
          <div className="flex items-center justify-center p-3 border-r">
            <input
              type="checkbox"
              checked={isAllSelected}
              ref={(el) => {
                if (el) el.indeterminate = isSomeSelected;
              }}
              onChange={(e) => handleSelectAll(e.target.checked)}
              className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
            />
          </div>
        )}
        {columns.map((column, index) => (
          <div
            key={String(column.key)}
            className={cn(
              'flex items-center p-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
              column.sortable && 'cursor-pointer hover:bg-gray-100',
              column.headerClassName,
              index < columns.length - 1 && 'border-r'
            )}
            onClick={() => column.sortable && handleSort(String(column.key))}
            style={{
              minWidth: column.minWidth,
              maxWidth: column.maxWidth,
            }}
          >
            <span className="truncate">{column.header}</span>
            {column.sortable && (
              <span className="ml-1">
                {sortKey === column.key ? (
                  sortDirection === 'asc' ? '↑' : '↓'
                ) : (
                  '↕'
                )}
              </span>
            )}
          </div>
        ))}
      </div>

      {/* Body */}
      <div
        ref={parentRef}
        className="overflow-auto"
        style={{ height }}
      >
        <div
          style={{
            height: `${rowVirtualizer.getTotalSize()}px`,
            width: '100%',
            position: 'relative',
          }}
        >
          {rowVirtualizer.getVirtualItems().map((virtualRow) => {
            const row = data[virtualRow.index];
            const rowId = getRowId(row, virtualRow.index);
            const isSelected = selectedRows.has(rowId);

            return (
              <div
                key={virtualRow.key}
                className={cn(
                  'absolute top-0 left-0 w-full grid border-b',
                  striped && virtualRow.index % 2 === 0 && 'bg-gray-50',
                  hoverable && 'hover:bg-blue-50',
                  isSelected && 'bg-blue-100',
                  onRowClick && 'cursor-pointer'
                )}
                style={{
                  height: `${virtualRow.size}px`,
                  transform: `translateY(${virtualRow.start}px)`,
                  gridTemplateColumns: columnWidths.join(' '),
                }}
                onClick={() => onRowClick?.(row, virtualRow.index)}
              >
                {selectable && (
                  <div className="flex items-center justify-center p-3 border-r">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={(e) => handleRowSelection(rowId, e.target.checked)}
                      onClick={(e) => e.stopPropagation()}
                      className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                    />
                  </div>
                )}
                {columns.map((column, colIndex) => {
                  const value = row[column.key as keyof T];
                  const content = column.render 
                    ? column.render(value, row, virtualRow.index)
                    : String(value || '');

                  return (
                    <div
                      key={String(column.key)}
                      className={cn(
                        'flex items-center p-3 text-sm text-gray-900',
                        column.className,
                        colIndex < columns.length - 1 && 'border-r'
                      )}
                      style={{
                        minWidth: column.minWidth,
                        maxWidth: column.maxWidth,
                      }}
                    >
                      <div className="truncate w-full">{content}</div>
                    </div>
                  );
                })}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export const VirtualizedTable = memo(VirtualizedTableComponent) as <T extends Record<string, any>>(
  props: VirtualizedTableProps<T>
) => JSX.Element;
