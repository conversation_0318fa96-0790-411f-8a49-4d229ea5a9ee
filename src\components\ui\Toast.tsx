import React, { useEffect } from 'react';
import { CheckCircle, AlertCircle, X, Info } from 'lucide-react';
import { Notification } from '../../types';

interface ToastProps {
  notification: Notification;
  onClose: (id: string | number) => void;
}

export const Toast: React.FC<ToastProps> = ({ notification, onClose }) => {
  const { id, message, type } = notification;

  useEffect(() => {
    const timer = setTimeout(() => {
      onClose(id);
    }, 5000);

    return () => clearTimeout(timer);
  }, [id, onClose]);

  const styles = {
    success: {
      bg: 'bg-green-500',
      icon: <CheckCircle className="h-6 w-6" />,
    },
    error: {
      bg: 'bg-red-500',
      icon: <AlertCircle className="h-6 w-6" />,
    },
    info: {
      bg: 'bg-blue-500',
      icon: <Info className="h-6 w-6" />,
    },
  };

  const currentStyle = styles[type] || styles.info;

  return (
    <div className={`relative flex items-center gap-4 w-full p-4 text-white rounded-lg shadow-lg animate-slide-down ${currentStyle.bg}`}>
      <div>{currentStyle.icon}</div>
      <p className="flex-1 text-sm font-medium whitespace-pre-wrap">{message}</p>
      <button 
        onClick={() => onClose(id)} 
        className="p-1 rounded-full hover:bg-white/20 transition-colors"
      >
        <X size={18} />
      </button>
    </div>
  );
};

interface ToastContainerProps {
  notifications: Notification[];
  removeNotification: (id: string | number) => void;
}

export const ToastContainer: React.FC<ToastContainerProps> = ({ 
  notifications, 
  removeNotification 
}) => {
  return (
    <div className="fixed top-5 right-5 z-[100] w-full max-w-sm space-y-3">
      {notifications.map(notification => (
        <Toast
          key={notification.id}
          notification={notification}
          onClose={removeNotification}
        />
      ))}
    </div>
  );
};
