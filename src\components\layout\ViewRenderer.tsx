import React, { Suspense, lazy } from 'react';
import { useApp } from '../../contexts/AppContext';
import { LoadingSpinner } from '../ui/LoadingSpinner';

// Lazy load views for better performance
const DashboardView = lazy(() => import('../views/DashboardView'));
const MainView = lazy(() => import('../views/MainView'));
const InstancesView = lazy(() => import('../views/InstancesView'));
const SettingsView = lazy(() => import('../views/SettingsView'));
const TemplatesView = lazy(() => import('../views/TemplatesView'));
const ContactsView = lazy(() => import('../views/ContactsView'));
const GroupsView = lazy(() => import('../views/GroupsView'));

export const ViewRenderer: React.FC = () => {
  const { state } = useApp();
  const { currentView } = state;

  const renderView = () => {
    switch (currentView) {
      case 'dashboard':
        return <DashboardView />;
      case 'main':
        return <MainView />;
      case 'instances':
        return <InstancesView />;
      case 'settings':
        return <SettingsView />;
      case 'templates':
        return <TemplatesView />;
      case 'contacts':
        return <ContactsView />;
      case 'groups':
        return <GroupsView />;
      default:
        return <DashboardView />;
    }
  };

  return (
    <Suspense fallback={<LoadingSpinner />}>
      {renderView()}
    </Suspense>
  );
};
