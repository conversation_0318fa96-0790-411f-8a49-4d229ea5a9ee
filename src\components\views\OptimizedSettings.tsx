import React, { useState, memo } from 'react';
import { useAppStore } from '@/stores/appStore';
import { Card } from '@/components/ui/Card';
import { OptimizedButton } from '@/components/ui/OptimizedButton';
import { ApiTester } from '@/components/ApiTester';
import { toast } from 'sonner';
import { 
  Settings as SettingsIcon, 
  Sparkles, 
  Save, 
  TestTube,
  Server,
  Key,
  Clock,
  Brain,
  Shield,
  Palette
} from 'lucide-react';

interface OptimizedSettingsProps {
  onSwitchView: (view: string) => void;
}

const SectionTitle = memo(({ 
  icon: Icon, 
  title, 
  subtitle 
}: { 
  icon: React.ComponentType<any>; 
  title: string; 
  subtitle?: string; 
}) => (
  <div className="flex items-center gap-3 mb-6">
    <div className="bg-blue-100 p-2 rounded-lg">
      <Icon className="h-5 w-5 text-blue-600" />
    </div>
    <div>
      <h3 className="font-semibold text-gray-800">{title}</h3>
      {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
    </div>
  </div>
));

SectionTitle.displayName = 'SectionTitle';

const OptimizedSettingsComponent: React.FC<OptimizedSettingsProps> = ({ onSwitchView }) => {
  const settings = useAppStore(state => state.settings);
  const updateSettings = useAppStore(state => state.updateSettings);
  const resetSettings = useAppStore(state => state.resetSettings);
  
  const [localSettings, setLocalSettings] = useState(settings);
  const [showApiTester, setShowApiTester] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      // Validações básicas
      if (!localSettings.baseUrl) {
        toast.error('URL Base da API é obrigatória');
        return;
      }
      
      if (!localSettings.apiKey) {
        toast.error('API Key é obrigatória');
        return;
      }

      // Validar URL
      try {
        new URL(localSettings.baseUrl);
      } catch {
        toast.error('URL Base da API é inválida');
        return;
      }

      // Validar delays
      const minDelay = parseFloat(localSettings.minDelay);
      const maxDelay = parseFloat(localSettings.maxDelay);
      
      if (minDelay < 0 || maxDelay < 0) {
        toast.error('Delays devem ser números positivos');
        return;
      }
      
      if (minDelay > maxDelay) {
        toast.error('Delay mínimo não pode ser maior que o máximo');
        return;
      }

      // Salvar configurações
      updateSettings(localSettings);
      
      toast.success('Configurações salvas com sucesso!');
      
      // Voltar para dashboard após salvar
      setTimeout(() => {
        onSwitchView('dashboard');
      }, 1000);
      
    } catch (error) {
      toast.error('Erro ao salvar configurações');
      console.error('Settings save error:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    if (confirm('Tem certeza que deseja resetar todas as configurações?')) {
      resetSettings();
      setLocalSettings({
        baseUrl: '',
        apiKey: '',
        minDelay: '2',
        maxDelay: '5',
        aiProvider: 'gemini',
        aiApiKey: '',
        systemPrompt: 'Você é um especialista em marketing para WhatsApp. Crie mensagens curtas, claras e persuasivas, usando formatação como negrito e itálico para destacar pontos importantes. A mensagem deve ser amigável e terminar com uma chamada para ação clara.'
      });
      toast.success('Configurações resetadas');
    }
  };

  const updateLocalSetting = (key: keyof typeof localSettings, value: string) => {
    setLocalSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Configurações</h1>
          <p className="text-gray-600">Configure sua API e preferências do sistema</p>
        </div>
        <OptimizedButton
          onClick={() => setShowApiTester(!showApiTester)}
          variant="outline"
          leftIcon={<TestTube size={18} />}
        >
          {showApiTester ? 'Ocultar' : 'Testar'} API
        </OptimizedButton>
      </div>

      {/* API Tester */}
      {showApiTester && <ApiTester />}

      {/* Configurações da API */}
      <Card className="p-6">
        <SectionTitle 
          icon={Server} 
          title="Configurações da API" 
          subtitle="Configure a conexão com sua API WhatsApp"
        />
        
        <div className="space-y-6">
          <div>
            <label htmlFor="baseUrl" className="block text-sm font-medium text-gray-700 mb-2">
              URL Base da API *
            </label>
            <input
              type="url"
              id="baseUrl"
              value={localSettings.baseUrl}
              onChange={(e) => updateLocalSetting('baseUrl', e.target.value)}
              placeholder="https://sua-api.com ou http://localhost:8080"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">
              URL completa da sua API WhatsApp (Evolution API, Baileys, etc.)
            </p>
          </div>

          <div>
            <label htmlFor="apiKey" className="block text-sm font-medium text-gray-700 mb-2">
              API Key *
            </label>
            <input
              type="password"
              id="apiKey"
              value={localSettings.apiKey}
              onChange={(e) => updateLocalSetting('apiKey', e.target.value)}
              placeholder="Sua chave de API"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">
              Chave de autenticação fornecida pela sua API
            </p>
          </div>
        </div>
      </Card>

      {/* Configurações de Timing */}
      <Card className="p-6">
        <SectionTitle 
          icon={Clock} 
          title="Configurações de Timing" 
          subtitle="Configure os delays entre envios para evitar bloqueios"
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="minDelay" className="block text-sm font-medium text-gray-700 mb-2">
              Delay Mínimo (segundos)
            </label>
            <input
              type="number"
              id="minDelay"
              min="0"
              step="0.5"
              value={localSettings.minDelay}
              onChange={(e) => updateLocalSetting('minDelay', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <div>
            <label htmlFor="maxDelay" className="block text-sm font-medium text-gray-700 mb-2">
              Delay Máximo (segundos)
            </label>
            <input
              type="number"
              id="maxDelay"
              min="0"
              step="0.5"
              value={localSettings.maxDelay}
              onChange={(e) => updateLocalSetting('maxDelay', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
        
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Recomendação:</strong> Use delays entre 2-5 segundos para evitar bloqueios. 
            O sistema escolherá um valor aleatório entre o mínimo e máximo.
          </p>
        </div>
      </Card>

      {/* Configurações de IA */}
      <Card className="p-6">
        <SectionTitle 
          icon={Brain} 
          title="Inteligência Artificial" 
          subtitle="Configure o provedor de IA para geração de mensagens"
        />
        
        <div className="space-y-6">
          <div>
            <label htmlFor="aiProvider" className="block text-sm font-medium text-gray-700 mb-2">
              Provedor de IA
            </label>
            <select
              id="aiProvider"
              value={localSettings.aiProvider}
              onChange={(e) => updateLocalSetting('aiProvider', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="gemini">Google Gemini</option>
              <option value="openai">OpenAI GPT</option>
            </select>
          </div>

          <div>
            <label htmlFor="aiApiKey" className="block text-sm font-medium text-gray-700 mb-2">
              Chave da API de IA
            </label>
            <input
              type="password"
              id="aiApiKey"
              value={localSettings.aiApiKey}
              onChange={(e) => updateLocalSetting('aiApiKey', e.target.value)}
              placeholder="Cole sua chave da API aqui"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">
              Opcional: Para usar a geração automática de mensagens com IA
            </p>
          </div>

          <div>
            <label htmlFor="systemPrompt" className="block text-sm font-medium text-gray-700 mb-2">
              Prompt do Sistema
            </label>
            <textarea
              id="systemPrompt"
              rows={4}
              value={localSettings.systemPrompt}
              onChange={(e) => updateLocalSetting('systemPrompt', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Defina como a IA deve se comportar ao gerar mensagens..."
            />
            <p className="text-xs text-gray-500 mt-1">
              Define a personalidade e estilo da IA ao gerar mensagens
            </p>
          </div>
        </div>
      </Card>

      {/* Ações */}
      <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t">
        <OptimizedButton
          onClick={handleSave}
          loading={isSaving}
          variant="default"
          fullWidth
          leftIcon={<Save size={18} />}
        >
          {isSaving ? 'Salvando...' : 'Salvar Configurações'}
        </OptimizedButton>
        
        <OptimizedButton
          onClick={handleReset}
          variant="outline"
          fullWidth
          disabled={isSaving}
        >
          Resetar Configurações
        </OptimizedButton>
      </div>

      {/* Informações de Ajuda */}
      <Card className="p-6 bg-gray-50">
        <h4 className="font-medium text-gray-700 mb-3">💡 Dicas de Configuração</h4>
        <div className="space-y-2 text-sm text-gray-600">
          <p>• <strong>URL da API:</strong> Deve incluir protocolo (http:// ou https://)</p>
          <p>• <strong>API Key:</strong> Chave fornecida pela sua API WhatsApp</p>
          <p>• <strong>Delays:</strong> Intervalos entre mensagens para evitar bloqueios</p>
          <p>• <strong>IA:</strong> Opcional, para geração automática de mensagens</p>
        </div>
      </Card>
    </div>
  );
};

export const OptimizedSettings = memo(OptimizedSettingsComponent);
