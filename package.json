{"name": "whatsapp-sender-optimized", "version": "2.0.0", "description": "Sistema Otimizado de Disparador WhatsApp em Massa - Versão Escalável", "license": "MIT", "homepage": "https://github.com/whatsapp-sender-optimized", "author": {"name": "WhatsApp Sender Team", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/whatsapp-sender-optimized.git"}, "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "public", "README.md"], "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "clean": "rimraf .next dist coverage", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "prepare": "husky install", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "zustand": "^4.4.7", "@tanstack/react-query": "^5.17.9", "@tanstack/react-virtual": "^3.0.1", "framer-motion": "^10.16.16", "lucide-react": "^0.303.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "class-variance-authority": "^0.7.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "papaparse": "^5.4.1", "idb": "^7.1.1", "comlink": "^4.4.1", "react-error-boundary": "^4.0.11", "react-intersection-observer": "^9.5.3", "use-debounce": "^10.0.0", "react-hotkeys-hook": "^4.4.1", "sonner": "^1.3.1"}, "devDependencies": {"@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@types/papaparse": "^5.3.14", "typescript": "^5.3.3", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "prettier": "^3.1.1", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@next/bundle-analyzer": "^14.0.4", "cross-env": "^7.0.3", "rimraf": "^5.0.5", "husky": "^8.0.3", "lint-staged": "^15.2.0", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.2.0", "jest-environment-jsdom": "^29.7.0", "@storybook/react": "^7.6.6", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/nextjs": "^7.6.6", "@storybook/testing-library": "^0.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}