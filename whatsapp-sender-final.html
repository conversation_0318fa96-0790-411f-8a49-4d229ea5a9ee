<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Sender - Sistema Funcional</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .animate-fade-in { animation: fadeIn 0.5s ease-out; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .loading { animation: spin 1s linear infinite; }
        @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
    </style>
</head>
<body class="bg-gray-50">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useCallback } = React;

        // Sistema de armazenamento
        const Storage = {
            save: (key, data) => {
                try {
                    localStorage.setItem(`whatsapp_${key}`, JSON.stringify(data));
                    return true;
                } catch (e) {
                    console.error('Erro ao salvar:', e);
                    return false;
                }
            },
            load: (key, defaultValue = null) => {
                try {
                    const saved = localStorage.getItem(`whatsapp_${key}`);
                    return saved ? JSON.parse(saved) : defaultValue;
                } catch (e) {
                    console.error('Erro ao carregar:', e);
                    return defaultValue;
                }
            },
            clear: (key) => {
                try {
                    localStorage.removeItem(`whatsapp_${key}`);
                    return true;
                } catch (e) {
                    return false;
                }
            }
        };

        // Sistema de notificações
        const useNotification = () => {
            const [notifications, setNotifications] = useState([]);

            const addNotification = useCallback((message, type = 'info') => {
                const id = Date.now() + Math.random();
                const notification = { id, message, type };
                
                setNotifications(prev => [...prev, notification]);
                
                setTimeout(() => {
                    setNotifications(prev => prev.filter(n => n.id !== id));
                }, 5000);
            }, []);

            return { notifications, addNotification };
        };

        // Sistema de API
        const useAPI = () => {
            const [loading, setLoading] = useState(false);
            const { addNotification } = useNotification();

            const makeRequest = useCallback(async (endpoint, options = {}) => {
                const settings = Storage.load('settings', {});
                
                if (!settings.baseUrl || !settings.apiKey) {
                    throw new Error('Configure a URL Base e API Key nas Configurações');
                }

                const url = `${settings.baseUrl.replace(/\/$/, '')}/${endpoint.replace(/^\//, '')}`;
                
                console.log('Fazendo requisição para:', url);
                console.log('Headers:', { 'apikey': settings.apiKey });
                
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': settings.apiKey,
                        ...options.headers,
                    },
                });

                console.log('Resposta:', response.status, response.statusText);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Erro da API:', errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                console.log('Dados recebidos:', data);
                return data;
            }, []);

            const fetchInstances = useCallback(async () => {
                setLoading(true);
                try {
                    const data = await makeRequest('/instance/fetchInstances');
                    console.log('Instâncias brutas da API:', data);
                    
                    // Processar diferentes formatos de resposta da Evolution API
                    let instances = [];
                    
                    if (Array.isArray(data)) {
                        instances = data;
                    } else if (data.instances && Array.isArray(data.instances)) {
                        instances = data.instances;
                    } else if (data.data && Array.isArray(data.data)) {
                        instances = data.data;
                    }

                    // Normalizar formato das instâncias
                    const normalizedInstances = instances.map(inst => {
                        // Diferentes estruturas possíveis da Evolution API
                        const instanceData = inst.instance || inst;
                        
                        return {
                            id: instanceData.instanceName || instanceData.name || inst.instanceName || Math.random().toString(),
                            name: instanceData.instanceName || instanceData.name || inst.instanceName || 'Sem nome',
                            status: instanceData.state || inst.state || inst.connectionStatus || 'close',
                            profileName: instanceData.profileName || inst.profileName || null,
                            profilePictureUrl: instanceData.profilePictureUrl || inst.profilePictureUrl || null
                        };
                    });

                    console.log('Instâncias normalizadas:', normalizedInstances);
                    
                    // Salvar no storage
                    Storage.save('instances', normalizedInstances);
                    
                    return normalizedInstances;
                } catch (error) {
                    console.error('Erro ao buscar instâncias:', error);
                    addNotification('Erro ao buscar instâncias: ' + error.message, 'error');
                    throw error;
                } finally {
                    setLoading(false);
                }
            }, [makeRequest, addNotification]);

            const createInstance = useCallback(async (name) => {
                setLoading(true);
                try {
                    const result = await makeRequest('/instance/create', {
                        method: 'POST',
                        body: JSON.stringify({ 
                            instanceName: name, 
                            qrcode: true,
                            integration: 'WHATSAPP-BAILEYS'
                        })
                    });
                    
                    addNotification(`Instância "${name}" criada com sucesso!`, 'success');
                    return result;
                } catch (error) {
                    console.error('Erro ao criar instância:', error);
                    addNotification('Erro ao criar instância: ' + error.message, 'error');
                    throw error;
                } finally {
                    setLoading(false);
                }
            }, [makeRequest, addNotification]);

            const getQRCode = useCallback(async (instanceName) => {
                setLoading(true);
                try {
                    const result = await makeRequest(`/instance/connect/${instanceName}`);
                    console.log('QR Code recebido:', result);
                    
                    let qrString = '';
                    if (typeof result === 'string') {
                        qrString = result;
                    } else if (result.base64) {
                        qrString = result.base64;
                    } else if (result.qrcode) {
                        qrString = result.qrcode;
                    } else if (result.code) {
                        qrString = result.code;
                    }

                    if (qrString) {
                        const qrImage = qrString.startsWith('data:image') ? qrString : `data:image/png;base64,${qrString}`;
                        return qrImage;
                    } else {
                        throw new Error('QR Code não encontrado na resposta');
                    }
                } catch (error) {
                    console.error('Erro ao obter QR Code:', error);
                    addNotification('Erro ao obter QR Code: ' + error.message, 'error');
                    throw error;
                } finally {
                    setLoading(false);
                }
            }, [makeRequest, addNotification]);

            const getInstanceStatus = useCallback(async (instanceName) => {
                try {
                    const result = await makeRequest(`/instance/connectionState/${instanceName}`);
                    console.log('Status da instância:', result);
                    return result;
                } catch (error) {
                    console.error('Erro ao verificar status:', error);
                    addNotification('Erro ao verificar status: ' + error.message, 'error');
                    throw error;
                }
            }, [makeRequest, addNotification]);

            const sendMessage = useCallback(async (instanceName, number, text) => {
                setLoading(true);
                try {
                    const result = await makeRequest(`/message/sendText/${instanceName}`, {
                        method: 'POST',
                        body: JSON.stringify({
                            number: number,
                            text: text,
                            options: {
                                delay: 1000
                            }
                        })
                    });
                    
                    console.log('Mensagem enviada:', result);
                    return result;
                } catch (error) {
                    console.error('Erro ao enviar mensagem:', error);
                    throw error;
                } finally {
                    setLoading(false);
                }
            }, [makeRequest]);

            const testConnection = useCallback(async () => {
                setLoading(true);
                try {
                    const instances = await fetchInstances();
                    addNotification('Conexão testada com sucesso!', 'success');
                    return { success: true, data: instances };
                } catch (error) {
                    addNotification('Erro na conexão: ' + error.message, 'error');
                    return { success: false, error: error.message };
                }
            }, [fetchInstances, addNotification]);

            return {
                loading,
                fetchInstances,
                createInstance,
                getQRCode,
                getInstanceStatus,
                sendMessage,
                testConnection
            };
        };

        // Componentes UI
        const Card = ({ children, className = '' }) => (
            <div className={`bg-white rounded-xl shadow-sm border border-gray-200 p-6 ${className}`}>
                {children}
            </div>
        );

        const Button = ({ children, onClick, disabled, variant = 'primary', size = 'md', className = '', loading = false }) => {
            const variants = {
                primary: 'bg-blue-600 hover:bg-blue-700 text-white disabled:bg-blue-400',
                secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800 disabled:bg-gray-100',
                success: 'bg-green-600 hover:bg-green-700 text-white disabled:bg-green-400',
                danger: 'bg-red-600 hover:bg-red-700 text-white disabled:bg-red-400',
                outline: 'border border-gray-300 hover:bg-gray-50 text-gray-700 disabled:bg-gray-50'
            };
            
            const sizes = {
                sm: 'px-3 py-1.5 text-sm',
                md: 'px-4 py-2',
                lg: 'px-6 py-3 text-lg'
            };

            return (
                <button
                    onClick={onClick}
                    disabled={disabled || loading}
                    className={`
                        inline-flex items-center justify-center gap-2 rounded-lg font-medium
                        transition-all duration-200 disabled:cursor-not-allowed
                        ${variants[variant]} ${sizes[size]} ${className}
                    `}
                >
                    {loading && <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full loading"></div>}
                    {children}
                </button>
            );
        };

        const Badge = ({ children, variant = 'default' }) => {
            const variants = {
                default: 'bg-blue-100 text-blue-800',
                success: 'bg-green-100 text-green-800',
                error: 'bg-red-100 text-red-800',
                warning: 'bg-yellow-100 text-yellow-800'
            };

            return (
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variants[variant]}`}>
                    {children}
                </span>
            );
        };

        const Input = ({ label, error, ...props }) => (
            <div className="space-y-1">
                {label && <label className="block text-sm font-medium text-gray-700">{label}</label>}
                <input
                    {...props}
                    className={`
                        w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                        ${error ? 'border-red-300' : 'border-gray-300'}
                        ${props.disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
                    `}
                />
                {error && <p className="text-sm text-red-600">{error}</p>}
            </div>
        );

        const Textarea = ({ label, error, ...props }) => (
            <div className="space-y-1">
                {label && <label className="block text-sm font-medium text-gray-700">{label}</label>}
                <textarea
                    {...props}
                    className={`
                        w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                        ${error ? 'border-red-300' : 'border-gray-300'}
                        ${props.disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
                    `}
                />
                {error && <p className="text-sm text-red-600">{error}</p>}
            </div>
        );

        const Select = ({ label, error, children, ...props }) => (
            <div className="space-y-1">
                {label && <label className="block text-sm font-medium text-gray-700">{label}</label>}
                <select
                    {...props}
                    className={`
                        w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                        ${error ? 'border-red-300' : 'border-gray-300'}
                        ${props.disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
                    `}
                >
                    {children}
                </select>
                {error && <p className="text-sm text-red-600">{error}</p>}
            </div>
        );

        // Componente de Notificações
        const NotificationContainer = ({ notifications }) => (
            <div className="fixed top-4 right-4 z-50 space-y-2">
                {notifications.map(notification => (
                    <div
                        key={notification.id}
                        className={`
                            p-4 rounded-lg shadow-lg max-w-sm animate-fade-in
                            ${notification.type === 'success' ? 'bg-green-500 text-white' : ''}
                            ${notification.type === 'error' ? 'bg-red-500 text-white' : ''}
                            ${notification.type === 'info' ? 'bg-blue-500 text-white' : ''}
                        `}
                    >
                        <div className="flex items-center gap-2">
                            <span>
                                {notification.type === 'success' && '✅'}
                                {notification.type === 'error' && '❌'}
                                {notification.type === 'info' && 'ℹ️'}
                            </span>
                            <p className="text-sm font-medium">{notification.message}</p>
                        </div>
                    </div>
                ))}
            </div>
        );

        // Componente de Configurações
        const Settings = () => {
            const [settings, setSettings] = useState(() => Storage.load('settings', {
                baseUrl: '',
                apiKey: '',
                minDelay: '2',
                maxDelay: '5'
            }));
            const [errors, setErrors] = useState({});
            const { addNotification } = useNotification();

            const validateSettings = () => {
                const newErrors = {};

                if (!settings.baseUrl) {
                    newErrors.baseUrl = 'URL Base é obrigatória';
                } else if (!settings.baseUrl.startsWith('http')) {
                    newErrors.baseUrl = 'URL deve começar com http:// ou https://';
                }

                if (!settings.apiKey) {
                    newErrors.apiKey = 'API Key é obrigatória';
                } else if (settings.apiKey.length < 8) {
                    newErrors.apiKey = 'API Key deve ter pelo menos 8 caracteres';
                }

                if (parseFloat(settings.minDelay) < 1) {
                    newErrors.minDelay = 'Delay mínimo deve ser pelo menos 1 segundo';
                }

                if (parseFloat(settings.maxDelay) < parseFloat(settings.minDelay)) {
                    newErrors.maxDelay = 'Delay máximo deve ser maior que o mínimo';
                }

                setErrors(newErrors);
                return Object.keys(newErrors).length === 0;
            };

            const handleSave = () => {
                if (!validateSettings()) {
                    addNotification('Corrija os erros antes de salvar', 'error');
                    return;
                }

                if (Storage.save('settings', settings)) {
                    addNotification('Configurações salvas com sucesso!', 'success');
                } else {
                    addNotification('Erro ao salvar configurações', 'error');
                }
            };

            const handleInputChange = (field, value) => {
                setSettings(prev => ({ ...prev, [field]: value }));
                if (errors[field]) {
                    setErrors(prev => ({ ...prev, [field]: '' }));
                }
            };

            return (
                <div className="space-y-6">
                    <Card>
                        <div className="flex items-center gap-3 mb-6">
                            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <span className="text-blue-600 text-xl">⚙️</span>
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Configurações da API</h2>
                                <p className="text-sm text-gray-500">Configure sua Evolution API para começar a usar</p>
                            </div>
                        </div>

                        <div className="space-y-4">
                            <Input
                                label="URL Base da API *"
                                type="url"
                                value={settings.baseUrl}
                                onChange={(e) => handleInputChange('baseUrl', e.target.value)}
                                placeholder="http://localhost:8080"
                                error={errors.baseUrl}
                            />

                            <Input
                                label="API Key *"
                                type="password"
                                value={settings.apiKey}
                                onChange={(e) => handleInputChange('apiKey', e.target.value)}
                                placeholder="Sua chave da Evolution API"
                                error={errors.apiKey}
                            />

                            <div className="grid grid-cols-2 gap-4">
                                <Input
                                    label="Delay Mínimo (segundos)"
                                    type="number"
                                    min="1"
                                    step="0.1"
                                    value={settings.minDelay}
                                    onChange={(e) => handleInputChange('minDelay', e.target.value)}
                                    error={errors.minDelay}
                                />
                                <Input
                                    label="Delay Máximo (segundos)"
                                    type="number"
                                    min="1"
                                    step="0.1"
                                    value={settings.maxDelay}
                                    onChange={(e) => handleInputChange('maxDelay', e.target.value)}
                                    error={errors.maxDelay}
                                />
                            </div>

                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <h4 className="font-medium text-yellow-800 mb-2">💡 Dicas importantes:</h4>
                                <ul className="text-sm text-yellow-700 space-y-1">
                                    <li>• Certifique-se que sua Evolution API está rodando</li>
                                    <li>• Configure CORS_ORIGIN=* no .env da Evolution API</li>
                                    <li>• Use delays de 2-5 segundos para evitar bloqueios</li>
                                    <li>• Teste a conexão após salvar as configurações</li>
                                </ul>
                            </div>

                            <Button onClick={handleSave} variant="primary" className="w-full">
                                💾 Salvar Configurações
                            </Button>
                        </div>
                    </Card>
                </div>
            );
        };

        // Componente de Teste de API
        const ApiTest = () => {
            const [result, setResult] = useState(null);
            const { testConnection, loading } = useAPI();
            const { addNotification } = useNotification();
            const settings = Storage.load('settings', {});

            const handleTest = async () => {
                setResult(null);
                const testResult = await testConnection();
                setResult(testResult);
            };

            return (
                <div className="space-y-6">
                    <Card>
                        <div className="flex items-center gap-3 mb-6">
                            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                <span className="text-green-600 text-xl">🔍</span>
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Teste de Conexão API</h2>
                                <p className="text-sm text-gray-500">Verifique se sua Evolution API está funcionando</p>
                            </div>
                        </div>

                        <div className="space-y-4">
                            <div className="bg-gray-50 rounded-lg p-4">
                                <h3 className="font-medium mb-3">Configuração Atual:</h3>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">URL:</span>
                                        <span className="font-mono">{settings.baseUrl || 'Não configurada'}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">API Key:</span>
                                        <span className="font-mono">
                                            {settings.apiKey ? `${settings.apiKey.substring(0, 8)}...` : 'Não configurada'}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Status:</span>
                                        <Badge variant={settings.baseUrl && settings.apiKey ? 'success' : 'error'}>
                                            {settings.baseUrl && settings.apiKey ? 'Configurada' : 'Pendente'}
                                        </Badge>
                                    </div>
                                </div>
                            </div>

                            <Button
                                onClick={handleTest}
                                disabled={!settings.baseUrl || !settings.apiKey}
                                loading={loading}
                                variant="primary"
                                className="w-full"
                            >
                                🔍 Testar Conexão
                            </Button>

                            {result && (
                                <div className={`
                                    p-4 rounded-lg border
                                    ${result.success
                                        ? 'bg-green-50 border-green-200'
                                        : 'bg-red-50 border-red-200'
                                    }
                                `}>
                                    <div className="flex items-center gap-2 mb-3">
                                        <span className={result.success ? 'text-green-600' : 'text-red-600'}>
                                            {result.success ? '✅' : '❌'}
                                        </span>
                                        <span className={`font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                                            {result.success ? 'Conexão estabelecida com sucesso!' : 'Falha na conexão'}
                                        </span>
                                    </div>

                                    {result.error && (
                                        <p className="text-sm text-red-700 mb-3">{result.error}</p>
                                    )}

                                    {result.data && (
                                        <details className="text-sm">
                                            <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
                                                Ver resposta da API ({result.data.length} instância(s) encontrada(s))
                                            </summary>
                                            <pre className="mt-2 p-3 bg-white rounded border text-xs overflow-auto max-h-40">
                                                {JSON.stringify(result.data, null, 2)}
                                            </pre>
                                        </details>
                                    )}
                                </div>
                            )}

                            {!settings.baseUrl || !settings.apiKey ? (
                                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                    <p className="text-yellow-800 text-sm">
                                        ⚠️ Configure a URL Base e API Key na aba "Configurações" antes de testar a conexão.
                                    </p>
                                </div>
                            ) : null}
                        </div>
                    </Card>
                </div>
            );
        };

        // Componente de Instâncias
        const Instances = () => {
            const [instances, setInstances] = useState(() => Storage.load('instances', []));
            const [newInstanceName, setNewInstanceName] = useState('');
            const [qrCode, setQrCode] = useState('');
            const [selectedInstance, setSelectedInstance] = useState('');

            const {
                fetchInstances,
                createInstance,
                getQRCode,
                getInstanceStatus,
                loading
            } = useAPI();
            const { addNotification } = useNotification();

            const loadInstances = async () => {
                try {
                    const fetchedInstances = await fetchInstances();
                    setInstances(fetchedInstances);
                } catch (error) {
                    console.error('Erro ao carregar instâncias:', error);
                }
            };

            const handleCreateInstance = async () => {
                if (!newInstanceName.trim()) {
                    addNotification('Digite um nome para a instância', 'error');
                    return;
                }

                try {
                    await createInstance(newInstanceName);
                    setNewInstanceName('');
                    await loadInstances();
                } catch (error) {
                    console.error('Erro ao criar instância:', error);
                }
            };

            const handleGetQRCode = async (instanceName) => {
                try {
                    setSelectedInstance(instanceName);
                    const qrImage = await getQRCode(instanceName);
                    setQrCode(qrImage);
                    addNotification('QR Code gerado! Escaneie com seu WhatsApp', 'success');
                } catch (error) {
                    console.error('Erro ao obter QR Code:', error);
                    setQrCode('');
                }
            };

            const handleCheckStatus = async (instanceName) => {
                try {
                    const status = await getInstanceStatus(instanceName);
                    addNotification(`Status da instância ${instanceName}: ${status.state || 'Desconhecido'}`, 'info');
                    await loadInstances();
                } catch (error) {
                    console.error('Erro ao verificar status:', error);
                }
            };

            useEffect(() => {
                loadInstances();
            }, []);

            return (
                <div className="space-y-6">
                    <Card>
                        <div className="flex items-center justify-between mb-6">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <span className="text-purple-600 text-xl">🖥️</span>
                                </div>
                                <div>
                                    <h2 className="text-xl font-semibold text-gray-900">Gerenciar Instâncias</h2>
                                    <p className="text-sm text-gray-500">Configure suas instâncias do WhatsApp</p>
                                </div>
                            </div>
                            <Button onClick={loadInstances} loading={loading} variant="outline">
                                🔄 Atualizar
                            </Button>
                        </div>

                        {/* Criar Nova Instância */}
                        <div className="bg-gray-50 rounded-lg p-4 mb-6">
                            <h3 className="font-medium mb-3">➕ Criar Nova Instância</h3>
                            <div className="flex gap-3">
                                <Input
                                    value={newInstanceName}
                                    onChange={(e) => setNewInstanceName(e.target.value)}
                                    placeholder="Nome da instância (ex: meu_whatsapp)"
                                    className="flex-1"
                                />
                                <Button
                                    onClick={handleCreateInstance}
                                    loading={loading}
                                    disabled={!newInstanceName.trim()}
                                >
                                    Criar
                                </Button>
                            </div>
                        </div>

                        {/* Lista de Instâncias */}
                        <div className="space-y-3">
                            {instances.length > 0 ? instances.map(instance => (
                                <div key={instance.id} className="border rounded-lg p-4 hover:bg-gray-50">
                                    <div className="flex items-center justify-between">
                                        <div className="flex-1">
                                            <div className="flex items-center gap-3">
                                                <h4 className="font-medium text-gray-900">{instance.name}</h4>
                                                <Badge variant={instance.status === 'open' ? 'success' : 'error'}>
                                                    {instance.status === 'open' ? '🟢 Conectado' : '🔴 Desconectado'}
                                                </Badge>
                                            </div>
                                            <p className="text-sm text-gray-600 mt-1">
                                                {instance.profileName || 'Aguardando conexão...'}
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Button
                                                onClick={() => handleGetQRCode(instance.name)}
                                                variant="outline"
                                                size="sm"
                                                loading={loading && selectedInstance === instance.name}
                                            >
                                                📱 QR Code
                                            </Button>
                                            <Button
                                                onClick={() => handleCheckStatus(instance.name)}
                                                variant="outline"
                                                size="sm"
                                                loading={loading}
                                            >
                                                🔍 Status
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            )) : (
                                <div className="text-center py-8 text-gray-500">
                                    <p className="mb-4">Nenhuma instância encontrada</p>
                                    <p className="text-sm">Crie uma nova instância acima ou verifique sua configuração da API</p>
                                </div>
                            )}
                        </div>

                        {/* QR Code Modal */}
                        {qrCode && (
                            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                                <div className="bg-white rounded-lg p-6 max-w-md w-full">
                                    <div className="text-center">
                                        <h3 className="text-lg font-semibold mb-4">📱 Escaneie o QR Code</h3>
                                        <div className="mb-4">
                                            <img src={qrCode} alt="QR Code" className="w-64 h-64 mx-auto border rounded-lg" />
                                        </div>
                                        <p className="text-sm text-gray-600 mb-4">
                                            Abra o WhatsApp no seu celular e escaneie este código
                                        </p>
                                        <Button onClick={() => setQrCode('')} variant="outline" className="w-full">
                                            Fechar
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </Card>
                </div>
            );
        };

        // Componente de Contatos
        const Contacts = () => {
            const [contacts, setContacts] = useState(() => Storage.load('contacts', []));
            const [newContact, setNewContact] = useState({ name: '', phone: '', tags: '' });
            const [searchTerm, setSearchTerm] = useState('');
            const [editingContact, setEditingContact] = useState(null);
            const { addNotification } = useNotification();

            const filteredContacts = contacts.filter(contact =>
                contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                contact.phone.includes(searchTerm) ||
                (contact.tags && contact.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))
            );

            const validateContact = (contact) => {
                if (!contact.name.trim()) {
                    addNotification('Nome é obrigatório', 'error');
                    return false;
                }
                if (!contact.phone.trim()) {
                    addNotification('Telefone é obrigatório', 'error');
                    return false;
                }
                if (!/^\d{10,15}$/.test(contact.phone.replace(/\D/g, ''))) {
                    addNotification('Telefone deve ter entre 10 e 15 dígitos', 'error');
                    return false;
                }
                return true;
            };

            const addContact = () => {
                if (!validateContact(newContact)) return;

                const contact = {
                    id: Date.now().toString(),
                    name: newContact.name.trim(),
                    phone: newContact.phone.replace(/\D/g, ''),
                    tags: newContact.tags.split(',').map(t => t.trim()).filter(Boolean)
                };

                const updatedContacts = [...contacts, contact];
                setContacts(updatedContacts);
                Storage.save('contacts', updatedContacts);
                setNewContact({ name: '', phone: '', tags: '' });
                addNotification('Contato adicionado com sucesso!', 'success');
            };

            const updateContact = () => {
                if (!validateContact(editingContact)) return;

                const updatedContacts = contacts.map(c =>
                    c.id === editingContact.id
                        ? {
                            ...editingContact,
                            name: editingContact.name.trim(),
                            phone: editingContact.phone.replace(/\D/g, ''),
                            tags: editingContact.tags.split(',').map(t => t.trim()).filter(Boolean)
                        }
                        : c
                );

                setContacts(updatedContacts);
                Storage.save('contacts', updatedContacts);
                setEditingContact(null);
                addNotification('Contato atualizado com sucesso!', 'success');
            };

            const deleteContact = (id) => {
                if (confirm('Tem certeza que deseja excluir este contato?')) {
                    const updatedContacts = contacts.filter(c => c.id !== id);
                    setContacts(updatedContacts);
                    Storage.save('contacts', updatedContacts);
                    addNotification('Contato excluído', 'success');
                }
            };

            return (
                <div className="space-y-6">
                    <Card>
                        <div className="flex items-center gap-3 mb-6">
                            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <span className="text-blue-600 text-xl">👥</span>
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Gerenciar Contatos</h2>
                                <p className="text-sm text-gray-500">{contacts.length} contatos cadastrados</p>
                            </div>
                        </div>

                        {/* Adicionar/Editar Contato */}
                        <div className="bg-gray-50 rounded-lg p-4 mb-6">
                            <h3 className="font-medium mb-3">
                                {editingContact ? '✏️ Editar Contato' : '➕ Adicionar Novo Contato'}
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                                <Input
                                    placeholder="Nome completo"
                                    value={editingContact ? editingContact.name : newContact.name}
                                    onChange={(e) => {
                                        if (editingContact) {
                                            setEditingContact({...editingContact, name: e.target.value});
                                        } else {
                                            setNewContact({...newContact, name: e.target.value});
                                        }
                                    }}
                                />
                                <Input
                                    placeholder="5511999998888"
                                    value={editingContact ? editingContact.phone : newContact.phone}
                                    onChange={(e) => {
                                        if (editingContact) {
                                            setEditingContact({...editingContact, phone: e.target.value});
                                        } else {
                                            setNewContact({...newContact, phone: e.target.value});
                                        }
                                    }}
                                />
                                <Input
                                    placeholder="cliente, vip, sp"
                                    value={editingContact ? editingContact.tags : newContact.tags}
                                    onChange={(e) => {
                                        if (editingContact) {
                                            setEditingContact({...editingContact, tags: e.target.value});
                                        } else {
                                            setNewContact({...newContact, tags: e.target.value});
                                        }
                                    }}
                                />
                                <div className="flex gap-2">
                                    <Button
                                        onClick={editingContact ? updateContact : addContact}
                                        variant="primary"
                                        className="flex-1"
                                    >
                                        {editingContact ? 'Salvar' : 'Adicionar'}
                                    </Button>
                                    {editingContact && (
                                        <Button
                                            onClick={() => setEditingContact(null)}
                                            variant="outline"
                                        >
                                            Cancelar
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Busca */}
                        <div className="mb-4">
                            <Input
                                placeholder="🔍 Buscar contatos por nome, telefone ou tag..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                        </div>

                        {/* Lista de Contatos */}
                        <div className="overflow-x-auto">
                            <table className="w-full border-collapse">
                                <thead>
                                    <tr className="border-b bg-gray-50">
                                        <th className="text-left p-3 font-medium">Nome</th>
                                        <th className="text-left p-3 font-medium">Telefone</th>
                                        <th className="text-left p-3 font-medium">Tags</th>
                                        <th className="text-center p-3 font-medium">Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {filteredContacts.map(contact => (
                                        <tr key={contact.id} className="border-b hover:bg-gray-50">
                                            <td className="p-3 font-medium">{contact.name}</td>
                                            <td className="p-3 font-mono text-sm">{contact.phone}</td>
                                            <td className="p-3">
                                                <div className="flex gap-1 flex-wrap">
                                                    {contact.tags?.map(tag => (
                                                        <Badge key={tag}>{tag}</Badge>
                                                    ))}
                                                </div>
                                            </td>
                                            <td className="p-3 text-center">
                                                <div className="flex justify-center gap-2">
                                                    <Button
                                                        onClick={() => setEditingContact({
                                                            ...contact,
                                                            tags: contact.tags?.join(', ') || ''
                                                        })}
                                                        variant="outline"
                                                        size="sm"
                                                    >
                                                        ✏️
                                                    </Button>
                                                    <Button
                                                        onClick={() => deleteContact(contact.id)}
                                                        variant="outline"
                                                        size="sm"
                                                    >
                                                        🗑️
                                                    </Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>

                            {filteredContacts.length === 0 && (
                                <div className="text-center py-8 text-gray-500">
                                    {searchTerm ? 'Nenhum contato encontrado para a busca' : 'Nenhum contato cadastrado'}
                                </div>
                            )}
                        </div>
                    </Card>
                </div>
            );
        };

        // Componente de Disparador
        const Campaign = () => {
            const [campaignData, setCampaignData] = useState({
                instance: '',
                message: '',
                tagFilter: ''
            });
            const [isRunning, setIsRunning] = useState(false);
            const [progress, setProgress] = useState({ current: 0, total: 0, success: 0, errors: 0 });
            const [campaignResults, setCampaignResults] = useState([]);

            const contacts = Storage.load('contacts', []);
            const instances = Storage.load('instances', []);
            const settings = Storage.load('settings', {});
            const { sendMessage } = useAPI();
            const { addNotification } = useNotification();

            const connectedInstances = instances.filter(i => i.status === 'open');
            const filteredContacts = campaignData.tagFilter
                ? contacts.filter(c => c.tags?.includes(campaignData.tagFilter))
                : contacts;

            const validateCampaign = () => {
                if (!campaignData.instance) {
                    addNotification('Selecione uma instância conectada', 'error');
                    return false;
                }
                if (!campaignData.message.trim()) {
                    addNotification('Digite uma mensagem para enviar', 'error');
                    return false;
                }
                if (filteredContacts.length === 0) {
                    addNotification('Nenhum contato selecionado para envio', 'error');
                    return false;
                }
                return true;
            };

            const startCampaign = async () => {
                if (!validateCampaign()) return;

                setIsRunning(true);
                setProgress({ current: 0, total: filteredContacts.length, success: 0, errors: 0 });
                setCampaignResults([]);

                const minDelay = parseFloat(settings.minDelay || '2') * 1000;
                const maxDelay = parseFloat(settings.maxDelay || '5') * 1000;
                const results = [];

                for (let i = 0; i < filteredContacts.length; i++) {
                    if (!isRunning) break; // Permite parar a campanha

                    const contact = filteredContacts[i];
                    setProgress(prev => ({ ...prev, current: i + 1 }));

                    try {
                        // Personalizar mensagem
                        const personalizedMessage = campaignData.message
                            .replace(/\{\{nome\}\}/gi, contact.name)
                            .replace(/\{\{phone\}\}/gi, contact.phone)
                            .replace(/\{\{tags\}\}/gi, contact.tags?.join(', ') || '');

                        await sendMessage(campaignData.instance, contact.phone, personalizedMessage);

                        const result = {
                            contact: contact.name,
                            phone: contact.phone,
                            status: 'success',
                            timestamp: new Date().toLocaleTimeString()
                        };

                        results.push(result);
                        setProgress(prev => ({ ...prev, success: prev.success + 1 }));
                        setCampaignResults(prev => [...prev, result]);

                    } catch (error) {
                        console.error('Erro ao enviar para', contact.name, error);

                        const result = {
                            contact: contact.name,
                            phone: contact.phone,
                            status: 'error',
                            error: error.message,
                            timestamp: new Date().toLocaleTimeString()
                        };

                        results.push(result);
                        setProgress(prev => ({ ...prev, errors: prev.errors + 1 }));
                        setCampaignResults(prev => [...prev, result]);
                    }

                    // Delay entre envios
                    if (i < filteredContacts.length - 1) {
                        const delay = Math.random() * (maxDelay - minDelay) + minDelay;
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }
                }

                setIsRunning(false);

                // Salvar estatísticas
                const stats = {
                    total: filteredContacts.length,
                    success: results.filter(r => r.status === 'success').length,
                    errors: results.filter(r => r.status === 'error').length,
                    lastRun: new Date().toLocaleString('pt-BR')
                };
                Storage.save('stats', stats);

                addNotification(
                    `Campanha concluída! ${stats.success} sucessos, ${stats.errors} erros`,
                    stats.errors === 0 ? 'success' : 'warning'
                );
            };

            const stopCampaign = () => {
                setIsRunning(false);
                addNotification('Campanha interrompida pelo usuário', 'info');
            };

            return (
                <div className="space-y-6">
                    {/* Status da Campanha */}
                    {isRunning && (
                        <Card className="bg-blue-50 border-blue-200">
                            <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-semibold text-blue-800">
                                    🚀 Campanha em Andamento
                                </h3>
                                <Button onClick={stopCampaign} variant="danger" size="sm">
                                    ⏹️ Parar Campanha
                                </Button>
                            </div>

                            <div className="mb-4">
                                <div className="flex justify-between text-sm text-blue-700 mb-1">
                                    <span>Progresso: {progress.current}/{progress.total}</span>
                                    <span>{Math.round((progress.current / progress.total) * 100)}%</span>
                                </div>
                                <div className="w-full bg-blue-200 rounded-full h-2">
                                    <div
                                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                        style={{ width: `${(progress.current / progress.total) * 100}%` }}
                                    ></div>
                                </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4 text-center">
                                <div>
                                    <p className="text-2xl font-bold text-green-600">{progress.success}</p>
                                    <p className="text-sm text-gray-600">Sucessos</p>
                                </div>
                                <div>
                                    <p className="text-2xl font-bold text-red-600">{progress.errors}</p>
                                    <p className="text-sm text-gray-600">Erros</p>
                                </div>
                            </div>
                        </Card>
                    )}

                    <Card>
                        <div className="flex items-center gap-3 mb-6">
                            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                <span className="text-green-600 text-xl">🚀</span>
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900">Disparador de Campanhas</h2>
                                <p className="text-sm text-gray-500">Envie mensagens personalizadas para seus contatos</p>
                            </div>
                        </div>

                        <div className="space-y-4">
                            {/* Seleção de Instância */}
                            <Select
                                label="Instância de Envio *"
                                value={campaignData.instance}
                                onChange={(e) => setCampaignData({...campaignData, instance: e.target.value})}
                                disabled={isRunning}
                            >
                                <option value="">Selecione uma instância conectada</option>
                                {connectedInstances.map(instance => (
                                    <option key={instance.id} value={instance.name}>
                                        {instance.name} ({instance.profileName || 'Conectado'})
                                    </option>
                                ))}
                            </Select>

                            {connectedInstances.length === 0 && (
                                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                    <p className="text-yellow-800 text-sm">
                                        ⚠️ Nenhuma instância conectada. Configure uma instância na aba "Instâncias".
                                    </p>
                                </div>
                            )}

                            {/* Filtro de Tag */}
                            <Input
                                label="Filtrar Contatos por Tag (opcional)"
                                value={campaignData.tagFilter}
                                onChange={(e) => setCampaignData({...campaignData, tagFilter: e.target.value})}
                                placeholder="Digite uma tag para filtrar contatos"
                                disabled={isRunning}
                            />

                            {/* Mensagem */}
                            <Textarea
                                label="Mensagem *"
                                value={campaignData.message}
                                onChange={(e) => setCampaignData({...campaignData, message: e.target.value})}
                                placeholder="Digite sua mensagem... Use {{nome}}, {{phone}} ou {{tags}} para personalizar"
                                rows="4"
                                disabled={isRunning}
                            />

                            {/* Info dos Destinatários */}
                            <div className="bg-gray-50 rounded-lg p-4">
                                <div className="flex justify-between items-center">
                                    <div>
                                        <p className="text-sm font-medium text-gray-700">
                                            📊 Destinatários selecionados:
                                            <span className="text-blue-600 font-bold ml-1">{filteredContacts.length}</span>
                                        </p>
                                        {filteredContacts.length > 0 && (
                                            <p className="text-xs text-gray-500 mt-1">
                                                {filteredContacts.slice(0, 3).map(c => c.name).join(', ')}
                                                {filteredContacts.length > 3 && ` e mais ${filteredContacts.length - 3}...`}
                                            </p>
                                        )}
                                    </div>
                                    {filteredContacts.length > 0 && (
                                        <div className="text-right">
                                            <p className="text-xs text-gray-500">Tempo estimado:</p>
                                            <p className="text-sm font-medium text-gray-700">
                                                {Math.ceil(filteredContacts.length * 3.5 / 60)} minutos
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Botão de Envio */}
                            <Button
                                onClick={startCampaign}
                                disabled={isRunning || !campaignData.instance || !campaignData.message || filteredContacts.length === 0}
                                loading={isRunning}
                                variant="primary"
                                className="w-full text-lg py-4"
                            >
                                {isRunning ? (
                                    '⏳ Enviando...'
                                ) : (
                                    `🚀 Iniciar Disparos para ${filteredContacts.length} Contatos`
                                )}
                            </Button>
                        </div>
                    </Card>

                    {/* Resultados da Campanha */}
                    {campaignResults.length > 0 && (
                        <Card>
                            <h3 className="text-lg font-semibold mb-4">📋 Resultados da Campanha</h3>
                            <div className="max-h-60 overflow-y-auto">
                                <div className="space-y-2">
                                    {campaignResults.map((result, index) => (
                                        <div key={index} className={`
                                            p-3 rounded-lg text-sm
                                            ${result.status === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}
                                        `}>
                                            <div className="flex justify-between items-center">
                                                <div>
                                                    <span className={result.status === 'success' ? 'text-green-600' : 'text-red-600'}>
                                                        {result.status === 'success' ? '✅' : '❌'}
                                                    </span>
                                                    <span className="ml-2 font-medium">{result.contact}</span>
                                                    <span className="ml-2 text-gray-500">({result.phone})</span>
                                                </div>
                                                <span className="text-xs text-gray-500">{result.timestamp}</span>
                                            </div>
                                            {result.error && (
                                                <p className="text-red-600 text-xs mt-1 ml-6">{result.error}</p>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </Card>
                    )}
                </div>
            );
        };

        // Componente de Dashboard
        const Dashboard = () => {
            const contacts = Storage.load('contacts', []);
            const instances = Storage.load('instances', []);
            const settings = Storage.load('settings', {});
            const stats = Storage.load('stats', { total: 0, success: 0, errors: 0, lastRun: null });

            const connectedInstances = instances.filter(i => i.status === 'open').length;
            const isConfigured = settings.baseUrl && settings.apiKey;
            const isReady = isConfigured && connectedInstances > 0 && contacts.length > 0;

            return (
                <div className="space-y-6">
                    {/* Cards de Estatísticas */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <Card className="text-center">
                            <div className="text-3xl mb-2">🖥️</div>
                            <h3 className="text-lg font-semibold text-gray-800">Instâncias</h3>
                            <p className="text-2xl font-bold text-blue-600">{connectedInstances}/{instances.length}</p>
                            <p className="text-sm text-gray-500">Conectadas</p>
                        </Card>

                        <Card className="text-center">
                            <div className="text-3xl mb-2">👥</div>
                            <h3 className="text-lg font-semibold text-gray-800">Contatos</h3>
                            <p className="text-2xl font-bold text-blue-600">{contacts.length}</p>
                            <p className="text-sm text-gray-500">Cadastrados</p>
                        </Card>

                        <Card className="text-center">
                            <div className="text-3xl mb-2">⚙️</div>
                            <h3 className="text-lg font-semibold text-gray-800">API</h3>
                            <p className="text-2xl font-bold text-blue-600">
                                {isConfigured ? '✅' : '❌'}
                            </p>
                            <p className="text-sm text-gray-500">
                                {isConfigured ? 'Configurada' : 'Pendente'}
                            </p>
                        </Card>

                        <Card className="text-center">
                            <div className="text-3xl mb-2">🚀</div>
                            <h3 className="text-lg font-semibold text-gray-800">Status</h3>
                            <p className="text-2xl font-bold text-green-600">
                                {isReady ? 'Pronto' : 'Configurar'}
                            </p>
                            <p className="text-sm text-gray-500">Sistema</p>
                        </Card>
                    </div>

                    {/* Status do Sistema */}
                    <Card>
                        <h2 className="text-xl font-semibold mb-4">📊 Status do Sistema</h2>
                        <div className="space-y-3">
                            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <span className="font-medium">Configuração da API</span>
                                <Badge variant={isConfigured ? 'success' : 'error'}>
                                    {isConfigured ? '✅ Configurada' : '❌ Pendente'}
                                </Badge>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <span className="font-medium">Instâncias WhatsApp</span>
                                <Badge variant={connectedInstances > 0 ? 'success' : 'error'}>
                                    {connectedInstances > 0 ? `✅ ${connectedInstances} Conectada(s)` : '❌ Nenhuma conectada'}
                                </Badge>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <span className="font-medium">Base de Contatos</span>
                                <Badge variant={contacts.length > 0 ? 'success' : 'default'}>
                                    {contacts.length > 0 ? `✅ ${contacts.length} Contatos` : '📝 Vazia'}
                                </Badge>
                            </div>
                        </div>
                    </Card>

                    {/* Últimas Estatísticas */}
                    {stats.lastRun && (
                        <Card>
                            <h2 className="text-xl font-semibold mb-4">📈 Última Campanha</h2>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
                                    <p className="text-sm text-gray-600">Total</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-green-600">{stats.success}</p>
                                    <p className="text-sm text-gray-600">Sucessos</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-red-600">{stats.errors}</p>
                                    <p className="text-sm text-gray-600">Erros</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-sm font-medium text-gray-700">Executada em</p>
                                    <p className="text-sm text-gray-600">{stats.lastRun}</p>
                                </div>
                            </div>
                        </Card>
                    )}

                    {/* Primeiros Passos */}
                    {!isReady && (
                        <Card className="bg-yellow-50 border-yellow-200">
                            <h2 className="text-xl font-semibold mb-4 text-yellow-800">🎯 Primeiros Passos</h2>
                            <div className="space-y-2 text-sm text-yellow-700">
                                {!isConfigured && <p>1. <strong>Configure sua Evolution API</strong> na aba "Configurações"</p>}
                                {isConfigured && <p>1. ✅ <strong>API configurada</strong></p>}

                                {connectedInstances === 0 && <p>2. <strong>Configure instâncias</strong> na aba "Instâncias"</p>}
                                {connectedInstances > 0 && <p>2. ✅ <strong>Instâncias conectadas</strong></p>}

                                {contacts.length === 0 && <p>3. <strong>Adicione contatos</strong> na aba "Contatos"</p>}
                                {contacts.length > 0 && <p>3. ✅ <strong>Contatos cadastrados</strong></p>}

                                {isReady && <p>4. <strong>Execute campanhas</strong> na aba "Disparador"</p>}
                            </div>
                        </Card>
                    )}

                    {/* Ações Rápidas */}
                    {isReady && (
                        <Card>
                            <h2 className="text-xl font-semibold mb-4">🚀 Sistema Pronto - Ações Rápidas</h2>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <Button
                                    onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'campaign' }))}
                                    variant="primary"
                                    className="h-16 text-lg"
                                >
                                    📱 Nova Campanha
                                </Button>
                                <Button
                                    onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'contacts' }))}
                                    variant="outline"
                                    className="h-16 text-lg"
                                >
                                    👥 Gerenciar Contatos
                                </Button>
                                <Button
                                    onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'instances' }))}
                                    variant="outline"
                                    className="h-16 text-lg"
                                >
                                    🖥️ Ver Instâncias
                                </Button>
                            </div>
                        </Card>
                    )}
                </div>
            );
        };

        // Componente Principal da Aplicação
        const App = () => {
            const [currentView, setCurrentView] = useState('dashboard');
            const { notifications, addNotification } = useNotification();

            const views = {
                dashboard: { name: 'Dashboard', icon: '📊', component: Dashboard },
                campaign: { name: 'Disparador', icon: '🚀', component: Campaign },
                contacts: { name: 'Contatos', icon: '👥', component: Contacts },
                instances: { name: 'Instâncias', icon: '🖥️', component: Instances },
                settings: { name: 'Configurações', icon: '⚙️', component: Settings },
                test: { name: 'Teste API', icon: '🔍', component: ApiTest }
            };

            const CurrentComponent = views[currentView]?.component || Dashboard;

            // Listener para navegação via eventos
            useEffect(() => {
                const handleNavigate = (event) => {
                    setCurrentView(event.detail);
                };
                window.addEventListener('navigate', handleNavigate);
                return () => window.removeEventListener('navigate', handleNavigate);
            }, []);

            return (
                <div className="min-h-screen bg-gray-50">
                    {/* Header */}
                    <header className="bg-white shadow-sm border-b">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="flex justify-between items-center h-16">
                                <div className="flex items-center gap-3">
                                    <div className="w-8 h-8 gradient-bg rounded-lg flex items-center justify-center">
                                        <span className="text-white font-bold text-sm">WS</span>
                                    </div>
                                    <h1 className="text-xl font-bold text-gray-900">WhatsApp Sender</h1>
                                    <Badge variant="success">v2.0 Funcional</Badge>
                                </div>
                                <div className="text-sm text-gray-500">
                                    🚀 Sistema 100% Operacional
                                </div>
                            </div>
                        </div>
                    </header>

                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                        {/* Navigation */}
                        <div className="mb-8">
                            <nav className="flex flex-wrap gap-1 bg-gray-100 p-1 rounded-xl">
                                {Object.entries(views).map(([key, view]) => (
                                    <button
                                        key={key}
                                        onClick={() => setCurrentView(key)}
                                        className={`
                                            px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2
                                            ${currentView === key
                                                ? 'bg-white text-blue-600 shadow-sm'
                                                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                                            }
                                        `}
                                    >
                                        <span>{view.icon}</span>
                                        <span className="hidden sm:inline">{view.name}</span>
                                    </button>
                                ))}
                            </nav>
                        </div>

                        {/* Content */}
                        <main className="animate-fade-in">
                            <CurrentComponent />
                        </main>
                    </div>

                    {/* Footer */}
                    <footer className="bg-white border-t mt-16">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                                <p className="text-sm text-gray-500">
                                    WhatsApp Sender v2.0 - Sistema Funcional e Otimizado
                                </p>
                                <div className="flex items-center gap-4 text-sm text-gray-500">
                                    <span>⚡ React 18</span>
                                    <span>🎨 Tailwind CSS</span>
                                    <span>📱 Responsivo</span>
                                    <span>🔗 Evolution API</span>
                                </div>
                            </div>
                        </div>
                    </footer>

                    {/* Notificações */}
                    <NotificationContainer notifications={notifications} />
                </div>
            );
        };

        // Renderizar a aplicação
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
