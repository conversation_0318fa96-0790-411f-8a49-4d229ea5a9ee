import React from 'react';

interface TableProps {
  children: React.ReactNode;
  className?: string;
}

export const Table: React.FC<TableProps> = ({ children, className = '' }) => {
  return (
    <div className="overflow-x-auto rounded-lg border border-gray-200">
      <table className={`min-w-full bg-white divide-y divide-gray-200 ${className}`}>
        {children}
      </table>
    </div>
  );
};

interface TableHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export const TableHeader: React.FC<TableHeaderProps> = ({ children, className = '' }) => {
  return (
    <thead className={`bg-gray-50 ${className}`}>
      {children}
    </thead>
  );
};

interface TableBodyProps {
  children: React.ReactNode;
  className?: string;
}

export const TableBody: React.FC<TableBodyProps> = ({ children, className = '' }) => {
  return (
    <tbody className={`divide-y divide-gray-200 ${className}`}>
      {children}
    </tbody>
  );
};

interface TableRowProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  selected?: boolean;
}

export const TableRow: React.FC<TableRowProps> = ({ 
  children, 
  className = '', 
  hover = true,
  selected = false 
}) => {
  const hoverClass = hover ? 'hover:bg-gray-50' : '';
  const selectedClass = selected ? 'bg-blue-50' : '';
  
  return (
    <tr className={`${hoverClass} ${selectedClass} ${className}`}>
      {children}
    </tr>
  );
};

interface TableCellProps {
  children: React.ReactNode;
  className?: string;
  align?: 'left' | 'center' | 'right';
  header?: boolean;
}

export const TableCell: React.FC<TableCellProps> = ({ 
  children, 
  className = '', 
  align = 'left',
  header = false 
}) => {
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };
  
  const baseClasses = `px-6 py-4 whitespace-nowrap ${alignClasses[align]}`;
  const headerClasses = header 
    ? 'text-xs font-medium text-gray-500 tracking-wider uppercase' 
    : 'text-sm text-gray-900';
  
  const Tag = header ? 'th' : 'td';
  
  return (
    <Tag className={`${baseClasses} ${headerClasses} ${className}`}>
      {children}
    </Tag>
  );
};

interface EmptyStateProps {
  message: string;
  colSpan: number;
  icon?: React.ReactNode;
}

export const TableEmptyState: React.FC<EmptyStateProps> = ({ 
  message, 
  colSpan, 
  icon 
}) => {
  return (
    <TableRow hover={false}>
      <TableCell className="text-center py-12 text-gray-500" align="center">
        <div className="flex flex-col items-center gap-3">
          {icon && <div className="text-gray-400">{icon}</div>}
          <span colSpan={colSpan}>{message}</span>
        </div>
      </TableCell>
    </TableRow>
  );
};
