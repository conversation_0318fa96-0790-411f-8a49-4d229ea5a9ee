# 📡 Modelos de Requisição - APIs WhatsApp

## 🎯 Configuração da API Key

### Variáveis de Ambiente
```bash
# .env.local
NEXT_PUBLIC_API_BASE_URL="https://sua-api.com"
NEXT_PUBLIC_API_KEY="sua-chave-aqui"
```

### Como a API Key é Usada
```typescript
// src/hooks/useAdvancedApi.ts
const response = await fetch(url, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'apikey': settings.apiKey,  // ← API Key aqui
    // ou 'Authorization': `Bearer ${settings.apiKey}`
  },
  body: JSON.stringify(payload)
});
```

## 🚀 Evolution API (Recomendada)

### 1. Criar Instância
```http
POST /instance/create
Content-Type: application/json
apikey: sua-chave-aqui

{
  "instanceName": "minha_instancia",
  "qrcode": true,
  "integration": "WHATSAPP-BAILEYS"
}
```

### 2. Conectar Instância (QR Code)
```http
GET /instance/connect/minha_instancia
apikey: sua-chave-aqui
```

**Resposta:**
```json
{
  "base64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "code": "2@abc123...",
  "count": 0
}
```

### 3. Status da Conexão
```http
GET /instance/connectionState/minha_instancia
apikey: sua-chave-aqui
```

**Resposta:**
```json
{
  "instance": {
    "instanceName": "minha_instancia",
    "state": "open"
  }
}
```

### 4. Enviar Mensagem de Texto
```http
POST /message/sendText/minha_instancia
Content-Type: application/json
apikey: sua-chave-aqui

{
  "number": "5511999998888",
  "text": "Olá! Esta é uma mensagem de teste.",
  "options": {
    "delay": 1200,
    "presence": "composing"
  }
}
```

### 5. Enviar Mídia (Imagem/Vídeo/Documento)
```http
POST /message/sendMedia/minha_instancia
Content-Type: application/json
apikey: sua-chave-aqui

{
  "number": "5511999998888",
  "mediatype": "image",
  "mimetype": "image/jpeg",
  "caption": "Legenda da imagem",
  "media": "base64-da-imagem-aqui",
  "fileName": "imagem.jpg",
  "options": {
    "delay": 1200
  }
}
```

### 6. Enviar Áudio
```http
POST /message/sendWhatsAppAudio/minha_instancia
Content-Type: application/json
apikey: sua-chave-aqui

{
  "number": "5511999998888",
  "audio": "base64-do-audio-aqui",
  "options": {
    "delay": 1200
  }
}
```

### 7. Buscar Contatos
```http
POST /chat/findContacts/minha_instancia
apikey: sua-chave-aqui
```

**Resposta:**
```json
[
  {
    "id": "<EMAIL>",
    "pushName": "João Silva",
    "remoteJid": "<EMAIL>"
  }
]
```

### 8. Buscar Grupos
```http
GET /group/fetchAllGroups/minha_instancia
apikey: sua-chave-aqui
```

**Resposta:**
```json
[
  {
    "id": "<EMAIL>",
    "subject": "Meu Grupo",
    "participants": [
      {
        "id": "<EMAIL>",
        "admin": "admin"
      }
    ]
  }
]
```

## 🔧 Baileys API (Alternativa)

### 1. Inicializar Sessão
```http
POST /api/session/start
Content-Type: application/json
Authorization: Bearer sua-chave-aqui

{
  "sessionId": "minha_sessao",
  "webhook": "https://seu-webhook.com/webhook"
}
```

### 2. Enviar Mensagem
```http
POST /api/message/send
Content-Type: application/json
Authorization: Bearer sua-chave-aqui

{
  "sessionId": "minha_sessao",
  "to": "<EMAIL>",
  "type": "text",
  "message": {
    "text": "Olá! Mensagem de teste."
  }
}
```

### 3. Enviar Mídia
```http
POST /api/message/send
Content-Type: application/json
Authorization: Bearer sua-chave-aqui

{
  "sessionId": "minha_sessao",
  "to": "<EMAIL>",
  "type": "media",
  "message": {
    "media": "base64-aqui",
    "mimetype": "image/jpeg",
    "caption": "Legenda"
  }
}
```

## 🌐 WhatsApp Business API (Oficial)

### 1. Enviar Mensagem Template
```http
POST /v17.0/PHONE_NUMBER_ID/messages
Content-Type: application/json
Authorization: Bearer ACCESS_TOKEN

{
  "messaging_product": "whatsapp",
  "to": "5511999998888",
  "type": "template",
  "template": {
    "name": "hello_world",
    "language": {
      "code": "pt_BR"
    }
  }
}
```

### 2. Enviar Mensagem de Texto
```http
POST /v17.0/PHONE_NUMBER_ID/messages
Content-Type: application/json
Authorization: Bearer ACCESS_TOKEN

{
  "messaging_product": "whatsapp",
  "recipient_type": "individual",
  "to": "5511999998888",
  "type": "text",
  "text": {
    "preview_url": false,
    "body": "Olá! Esta é uma mensagem de teste."
  }
}
```

## 🔄 Implementação no Sistema

### Hook useAdvancedApi.ts
```typescript
// Como o sistema usa a API
const { makeRequest } = useAdvancedApi();

// Enviar mensagem de texto
const response = await makeRequest('/message/sendText/instancia', {
  method: 'POST',
  body: JSON.stringify({
    number: '5511999998888',
    text: 'Mensagem personalizada',
    options: { delay: 2000 }
  })
});

// Enviar mídia
const response = await makeRequest('/message/sendMedia/instancia', {
  method: 'POST',
  body: JSON.stringify({
    number: '5511999998888',
    mediatype: 'image',
    mimetype: 'image/jpeg',
    media: base64Image,
    caption: 'Legenda da imagem',
    fileName: 'imagem.jpg'
  })
});
```

### Configuração Automática
```typescript
// src/stores/appStore.ts
const settings = {
  baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL,
  apiKey: process.env.NEXT_PUBLIC_API_KEY,
  minDelay: '2',
  maxDelay: '5'
};
```

## 🛡️ Segurança da API Key

### ❌ Nunca Faça Isso
```javascript
// ERRADO - API key exposta no frontend
const apiKey = "minha-chave-secreta";
```

### ✅ Forma Correta
```javascript
// CORRETO - API key em variável de ambiente
const apiKey = process.env.NEXT_PUBLIC_API_KEY;

// Ou melhor ainda - no backend
const apiKey = process.env.API_KEY; // Sem NEXT_PUBLIC_
```

### Proteção Adicional
```typescript
// Validação da API key
if (!settings.apiKey) {
  throw new Error('API Key não configurada');
}

// Rate limiting
const rateLimiter = new Map();
const checkRateLimit = (key: string) => {
  const now = Date.now();
  const requests = rateLimiter.get(key) || [];
  const recentRequests = requests.filter(time => now - time < 60000);
  
  if (recentRequests.length >= 60) { // 60 req/min
    throw new Error('Rate limit exceeded');
  }
  
  recentRequests.push(now);
  rateLimiter.set(key, recentRequests);
};
```

## 🔧 Configuração Rápida

### 1. Evolution API (Docker)
```bash
# docker-compose.yml
version: '3.8'
services:
  evolution-api:
    image: atendai/evolution-api:latest
    ports:
      - "8080:8080"
    environment:
      - SERVER_URL=http://localhost:8080
      - AUTHENTICATION_API_KEY=sua-chave-aqui
```

### 2. Configurar no Sistema
```bash
# .env.local
NEXT_PUBLIC_API_BASE_URL="http://localhost:8080"
NEXT_PUBLIC_API_KEY="sua-chave-aqui"
```

### 3. Testar Conexão
```typescript
// Teste rápido
const testApi = async () => {
  const response = await fetch(`${API_BASE_URL}/instance/fetchInstances`, {
    headers: { 'apikey': API_KEY }
  });
  console.log(await response.json());
};
```

## 📞 Suporte

### APIs Recomendadas
1. **Evolution API** - Mais completa e estável
2. **Baileys** - Open source, mais customizável
3. **WhatsApp Business API** - Oficial, para empresas

### Documentações
- [Evolution API](https://doc.evolutionapi.com)
- [Baileys](https://github.com/WhiskeySockets/Baileys)
- [WhatsApp Business API](https://developers.facebook.com/docs/whatsapp)

---

**✅ Configure sua API key e comece a enviar mensagens!**
