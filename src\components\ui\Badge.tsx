import React from 'react';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  className = '',
}) => {
  const baseClasses = 'inline-flex items-center font-medium rounded-full';
  
  const variantClasses = {
    default: 'bg-gray-100 text-gray-800',
    success: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    error: 'bg-red-100 text-red-800',
    info: 'bg-blue-100 text-blue-800',
  };
  
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base',
  };
  
  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;
  
  return (
    <span className={classes}>
      {children}
    </span>
  );
};

interface StatusBadgeProps {
  status: 'connected' | 'disconnected' | 'connecting';
  className?: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className = '' }) => {
  const statusConfig = {
    connected: {
      variant: 'success' as const,
      text: 'Conectado',
      dot: 'bg-green-500',
    },
    disconnected: {
      variant: 'error' as const,
      text: 'Desconectado',
      dot: 'bg-red-500',
    },
    connecting: {
      variant: 'warning' as const,
      text: 'Conectando',
      dot: 'bg-yellow-500',
    },
  };

  const config = statusConfig[status];

  return (
    <Badge variant={config.variant} className={`gap-1.5 ${className}`}>
      <span className={`h-1.5 w-1.5 rounded-full ${config.dot}`} />
      {config.text}
    </Badge>
  );
};
