import * as Comlink from 'comlink';

export interface CampaignWorkerAPI {
  processCampaign: (config: CampaignProcessConfig) => Promise<CampaignResult>;
  processContacts: (contacts: any[], filters: ContactFilters) => Promise<ProcessedContact[]>;
  generateMessages: (template: string, contacts: any[]) => Promise<GeneratedMessage[]>;
  validatePhoneNumbers: (phones: string[]) => Promise<ValidationResult[]>;
  optimizeImages: (images: ImageFile[]) => Promise<OptimizedImage[]>;
  parseCSV: (csvData: string) => Promise<ParsedCSVResult>;
}

interface CampaignProcessConfig {
  targets: any[];
  messages: any[];
  settings: any;
  filters?: any;
}

interface CampaignResult {
  processedTargets: any[];
  estimatedTime: number;
  warnings: string[];
  errors: string[];
}

interface ContactFilters {
  tags?: string[];
  dateRange?: { start: Date; end: Date };
  customFields?: Record<string, any>;
}

interface ProcessedContact {
  id: string;
  name: string;
  phone: string;
  isValid: boolean;
  tags: string[];
  customFields: Record<string, any>;
}

interface GeneratedMessage {
  contactId: string;
  text: string;
  variables: Record<string, string>;
}

interface ValidationResult {
  phone: string;
  isValid: boolean;
  formatted: string;
  country?: string;
  type?: 'mobile' | 'landline' | 'unknown';
}

interface ImageFile {
  data: ArrayBuffer;
  type: string;
  name: string;
}

interface OptimizedImage {
  data: ArrayBuffer;
  type: string;
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
}

interface ParsedCSVResult {
  data: any[];
  headers: string[];
  errors: string[];
  rowCount: number;
}

class CampaignWorker implements CampaignWorkerAPI {
  
  async processCampaign(config: CampaignProcessConfig): Promise<CampaignResult> {
    const { targets, messages, settings } = config;
    const processedTargets: any[] = [];
    const warnings: string[] = [];
    const errors: string[] = [];

    // Validate targets
    for (const target of targets) {
      try {
        const processed = await this.processTarget(target);
        if (processed.isValid) {
          processedTargets.push(processed);
        } else {
          warnings.push(`Target ${target.name || target.id} is invalid: ${processed.reason}`);
        }
      } catch (error) {
        errors.push(`Failed to process target ${target.name || target.id}: ${error}`);
      }
    }

    // Calculate estimated time
    const avgMessageTime = 3; // seconds per message
    const avgDelayTime = parseFloat(settings.minDelay || '2') + parseFloat(settings.maxDelay || '5') / 2;
    const estimatedTime = processedTargets.length * messages.length * (avgMessageTime + avgDelayTime);

    return {
      processedTargets,
      estimatedTime,
      warnings,
      errors,
    };
  }

  async processContacts(contacts: any[], filters: ContactFilters): Promise<ProcessedContact[]> {
    const processed: ProcessedContact[] = [];

    for (const contact of contacts) {
      // Apply filters
      if (filters.tags && filters.tags.length > 0) {
        const hasMatchingTag = contact.tags?.some((tag: string) => filters.tags!.includes(tag));
        if (!hasMatchingTag) continue;
      }

      if (filters.dateRange) {
        const contactDate = new Date(contact.createdAt || contact.updatedAt);
        if (contactDate < filters.dateRange.start || contactDate > filters.dateRange.end) {
          continue;
        }
      }

      if (filters.customFields) {
        const matchesCustomFields = Object.entries(filters.customFields).every(
          ([key, value]) => contact.customFields?.[key] === value
        );
        if (!matchesCustomFields) continue;
      }

      // Validate phone number
      const phoneValidation = await this.validatePhoneNumber(contact.phone);

      processed.push({
        id: contact.id,
        name: contact.name || 'Sem nome',
        phone: phoneValidation.formatted,
        isValid: phoneValidation.isValid,
        tags: contact.tags || [],
        customFields: contact.customFields || {},
      });
    }

    return processed;
  }

  async generateMessages(template: string, contacts: any[]): Promise<GeneratedMessage[]> {
    const generated: GeneratedMessage[] = [];

    for (const contact of contacts) {
      // Extract variables from template
      const variables: Record<string, string> = {};
      const variableRegex = /\{\{(\w+)\}\}/g;
      let match;

      while ((match = variableRegex.exec(template)) !== null) {
        const variableName = match[1];
        variables[variableName] = contact[variableName] || contact.customFields?.[variableName] || `{{${variableName}}}`;
      }

      // Replace variables in template
      let text = template;
      Object.entries(variables).forEach(([key, value]) => {
        text = text.replace(new RegExp(`\\{\\{${key}\\}\\}`, 'g'), value);
      });

      generated.push({
        contactId: contact.id,
        text,
        variables,
      });
    }

    return generated;
  }

  async validatePhoneNumbers(phones: string[]): Promise<ValidationResult[]> {
    return Promise.all(phones.map(phone => this.validatePhoneNumber(phone)));
  }

  private async validatePhoneNumber(phone: string): Promise<ValidationResult> {
    // Basic phone validation (can be enhanced with libphonenumber)
    const cleaned = phone.replace(/\D/g, '');
    
    // Brazilian phone number validation
    const isBrazilian = cleaned.startsWith('55') && cleaned.length >= 12;
    const isMobile = isBrazilian && cleaned.length === 13 && cleaned[4] === '9';
    
    return {
      phone,
      isValid: cleaned.length >= 10 && cleaned.length <= 15,
      formatted: cleaned,
      country: isBrazilian ? 'BR' : undefined,
      type: isMobile ? 'mobile' : cleaned.length >= 10 ? 'unknown' : 'unknown',
    };
  }

  async optimizeImages(images: ImageFile[]): Promise<OptimizedImage[]> {
    const optimized: OptimizedImage[] = [];

    for (const image of images) {
      try {
        // Create canvas for image processing
        const canvas = new OffscreenCanvas(800, 600); // Max dimensions
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          throw new Error('Cannot get canvas context');
        }

        // Create image from buffer
        const blob = new Blob([image.data], { type: image.type });
        const imageBitmap = await createImageBitmap(blob);

        // Calculate new dimensions maintaining aspect ratio
        const { width, height } = this.calculateOptimalDimensions(
          imageBitmap.width,
          imageBitmap.height,
          800,
          600
        );

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx.drawImage(imageBitmap, 0, 0, width, height);
        
        const optimizedBlob = await canvas.convertToBlob({
          type: 'image/jpeg',
          quality: 0.8,
        });

        const optimizedData = await optimizedBlob.arrayBuffer();

        optimized.push({
          data: optimizedData,
          type: 'image/jpeg',
          originalSize: image.data.byteLength,
          optimizedSize: optimizedData.byteLength,
          compressionRatio: image.data.byteLength / optimizedData.byteLength,
        });

        imageBitmap.close();
      } catch (error) {
        console.error('Failed to optimize image:', error);
        // Return original if optimization fails
        optimized.push({
          data: image.data,
          type: image.type,
          originalSize: image.data.byteLength,
          optimizedSize: image.data.byteLength,
          compressionRatio: 1,
        });
      }
    }

    return optimized;
  }

  async parseCSV(csvData: string): Promise<ParsedCSVResult> {
    const lines = csvData.split('\n').filter(line => line.trim());
    if (lines.length === 0) {
      return { data: [], headers: [], errors: ['CSV is empty'], rowCount: 0 };
    }

    const headers = this.parseCSVLine(lines[0]);
    const data: any[] = [];
    const errors: string[] = [];

    for (let i = 1; i < lines.length; i++) {
      try {
        const values = this.parseCSVLine(lines[i]);
        if (values.length !== headers.length) {
          errors.push(`Row ${i + 1}: Column count mismatch (expected ${headers.length}, got ${values.length})`);
          continue;
        }

        const row: any = {};
        headers.forEach((header, index) => {
          row[header] = values[index];
        });

        data.push(row);
      } catch (error) {
        errors.push(`Row ${i + 1}: ${error}`);
      }
    }

    return {
      data,
      headers,
      errors,
      rowCount: data.length,
    };
  }

  private parseCSVLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      const nextChar = line[i + 1];

      if (char === '"') {
        if (inQuotes && nextChar === '"') {
          current += '"';
          i++; // Skip next quote
        } else {
          inQuotes = !inQuotes;
        }
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }

    result.push(current.trim());
    return result;
  }

  private calculateOptimalDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidth: number,
    maxHeight: number
  ): { width: number; height: number } {
    const aspectRatio = originalWidth / originalHeight;

    let width = originalWidth;
    let height = originalHeight;

    if (width > maxWidth) {
      width = maxWidth;
      height = width / aspectRatio;
    }

    if (height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }

    return {
      width: Math.round(width),
      height: Math.round(height),
    };
  }

  private async processTarget(target: any): Promise<{ isValid: boolean; reason?: string }> {
    // Validate target based on type
    if (target.phone) {
      // Contact validation
      const phoneValidation = await this.validatePhoneNumber(target.phone);
      return {
        isValid: phoneValidation.isValid,
        reason: phoneValidation.isValid ? undefined : 'Invalid phone number',
      };
    } else if (target.id) {
      // Group validation
      return {
        isValid: true,
      };
    }

    return {
      isValid: false,
      reason: 'Missing required fields',
    };
  }
}

// Expose the worker API
Comlink.expose(new CampaignWorker());
