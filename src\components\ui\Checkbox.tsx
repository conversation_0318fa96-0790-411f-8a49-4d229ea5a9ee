import React, { forwardRef } from 'react';
import { Check, Minus } from 'lucide-react';

interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string;
  error?: string;
  helperText?: string;
  indeterminate?: boolean;
}

export const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(({
  label,
  error,
  helperText,
  indeterminate = false,
  className = '',
  ...props
}, ref) => {
  const baseClasses = 'h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2 focus:ring-offset-2 transition-colors duration-200';
  const errorClasses = error ? 'border-red-500 focus:ring-red-500' : '';
  
  const checkboxClasses = `${baseClasses} ${errorClasses} ${className}`;

  return (
    <div className="flex items-start gap-3">
      <div className="relative flex items-center">
        <input
          ref={ref}
          type="checkbox"
          className={checkboxClasses}
          {...props}
        />
        {indeterminate && (
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <Minus className="h-3 w-3 text-blue-600" />
          </div>
        )}
      </div>
      
      {(label || error || helperText) && (
        <div className="flex-1">
          {label && (
            <label className="block text-sm font-medium text-gray-700">
              {label}
            </label>
          )}
          
          {error && (
            <p className="mt-1 text-sm text-red-600">{error}</p>
          )}
          
          {helperText && !error && (
            <p className="mt-1 text-sm text-gray-500">{helperText}</p>
          )}
        </div>
      )}
    </div>
  );
});

Checkbox.displayName = 'Checkbox';
