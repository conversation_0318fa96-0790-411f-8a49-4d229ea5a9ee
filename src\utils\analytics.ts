interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp: number;
  sessionId: string;
  userId?: string;
}

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface ErrorEvent {
  message: string;
  stack?: string;
  component?: string;
  timestamp: number;
  sessionId: string;
  userId?: string;
  metadata?: Record<string, any>;
}

class Analytics {
  private sessionId: string;
  private userId?: string;
  private events: AnalyticsEvent[] = [];
  private performanceMetrics: PerformanceMetric[] = [];
  private errors: ErrorEvent[] = [];
  private isEnabled: boolean = true;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.initializePerformanceObserver();
    this.initializeErrorTracking();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Event tracking
  track(eventName: string, properties?: Record<string, any>) {
    if (!this.isEnabled) return;

    const event: AnalyticsEvent = {
      name: eventName,
      properties,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
    };

    this.events.push(event);
    this.sendEvent(event);

    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.log('[Analytics]', eventName, properties);
    }
  }

  // User identification
  identify(userId: string, traits?: Record<string, any>) {
    this.userId = userId;
    this.track('user_identified', { userId, ...traits });
  }

  // Page view tracking
  page(pageName: string, properties?: Record<string, any>) {
    this.track('page_view', {
      page: pageName,
      url: window.location.href,
      referrer: document.referrer,
      ...properties,
    });
  }

  // Performance tracking
  trackPerformance(metricName: string, value: number, metadata?: Record<string, any>) {
    const metric: PerformanceMetric = {
      name: metricName,
      value,
      timestamp: Date.now(),
      metadata,
    };

    this.performanceMetrics.push(metric);
    this.sendPerformanceMetric(metric);
  }

  // Error tracking
  trackError(error: Error, component?: string, metadata?: Record<string, any>) {
    const errorEvent: ErrorEvent = {
      message: error.message,
      stack: error.stack,
      component,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
      metadata,
    };

    this.errors.push(errorEvent);
    this.sendError(errorEvent);

    console.error('[Analytics Error]', error, { component, metadata });
  }

  // Campaign analytics
  trackCampaignEvent(eventType: string, campaignData: any) {
    this.track(`campaign_${eventType}`, {
      campaignId: campaignData.id,
      targetCount: campaignData.targets?.length,
      messageCount: campaignData.messages?.length,
      instance: campaignData.instance,
      sendMode: campaignData.sendMode,
    });
  }

  // Contact analytics
  trackContactEvent(eventType: string, contactData: any) {
    this.track(`contact_${eventType}`, {
      contactId: contactData.id,
      hasName: !!contactData.name,
      tagCount: contactData.tags?.length || 0,
      source: contactData.source,
    });
  }

  // Instance analytics
  trackInstanceEvent(eventType: string, instanceData: any) {
    this.track(`instance_${eventType}`, {
      instanceId: instanceData.id,
      instanceName: instanceData.name,
      connectionStatus: instanceData.connectionStatus,
    });
  }

  // Performance observer initialization
  private initializePerformanceObserver() {
    if (typeof window === 'undefined') return;

    // Web Vitals tracking
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.trackPerformance('lcp', entry.startTime, {
            element: entry.element?.tagName,
            url: entry.url,
          });
        }
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.trackPerformance('fid', entry.processingStart - entry.startTime, {
            eventType: entry.name,
          });
        }
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            this.trackPerformance('cls', entry.value, {
              sources: entry.sources?.map((s: any) => s.node?.tagName),
            });
          }
        }
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });

      // Navigation timing
      const navigationObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.trackPerformance('navigation', entry.duration, {
            type: entry.type,
            redirectCount: entry.redirectCount,
          });
        }
      });
      navigationObserver.observe({ entryTypes: ['navigation'] });
    }

    // Memory usage tracking
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        this.trackPerformance('memory_usage', memory.usedJSHeapSize, {
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
        });
      }, 30000); // Every 30 seconds
    }
  }

  // Error tracking initialization
  private initializeErrorTracking() {
    if (typeof window === 'undefined') return;

    // Global error handler
    window.addEventListener('error', (event) => {
      this.trackError(new Error(event.message), undefined, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      });
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.trackError(new Error(event.reason), undefined, {
        type: 'unhandled_promise_rejection',
      });
    });
  }

  // Send events to analytics service
  private async sendEvent(event: AnalyticsEvent) {
    try {
      // In a real app, you would send this to your analytics service
      // For now, we'll store it locally
      const events = JSON.parse(localStorage.getItem('analytics_events') || '[]');
      events.push(event);
      
      // Keep only last 1000 events
      if (events.length > 1000) {
        events.splice(0, events.length - 1000);
      }
      
      localStorage.setItem('analytics_events', JSON.stringify(events));
    } catch (error) {
      console.error('Failed to send analytics event:', error);
    }
  }

  private async sendPerformanceMetric(metric: PerformanceMetric) {
    try {
      const metrics = JSON.parse(localStorage.getItem('performance_metrics') || '[]');
      metrics.push(metric);
      
      // Keep only last 500 metrics
      if (metrics.length > 500) {
        metrics.splice(0, metrics.length - 500);
      }
      
      localStorage.setItem('performance_metrics', JSON.stringify(metrics));
    } catch (error) {
      console.error('Failed to send performance metric:', error);
    }
  }

  private async sendError(errorEvent: ErrorEvent) {
    try {
      const errors = JSON.parse(localStorage.getItem('error_events') || '[]');
      errors.push(errorEvent);
      
      // Keep only last 100 errors
      if (errors.length > 100) {
        errors.splice(0, errors.length - 100);
      }
      
      localStorage.setItem('error_events', JSON.stringify(errors));
    } catch (error) {
      console.error('Failed to send error event:', error);
    }
  }

  // Get analytics data
  getEvents(): AnalyticsEvent[] {
    try {
      return JSON.parse(localStorage.getItem('analytics_events') || '[]');
    } catch {
      return [];
    }
  }

  getPerformanceMetrics(): PerformanceMetric[] {
    try {
      return JSON.parse(localStorage.getItem('performance_metrics') || '[]');
    } catch {
      return [];
    }
  }

  getErrors(): ErrorEvent[] {
    try {
      return JSON.parse(localStorage.getItem('error_events') || '[]');
    } catch {
      return [];
    }
  }

  // Analytics summary
  getSummary() {
    const events = this.getEvents();
    const metrics = this.getPerformanceMetrics();
    const errors = this.getErrors();

    return {
      totalEvents: events.length,
      totalErrors: errors.length,
      sessionDuration: Date.now() - parseInt(this.sessionId.split('_')[1]),
      averagePerformance: {
        lcp: this.getAverageMetric(metrics, 'lcp'),
        fid: this.getAverageMetric(metrics, 'fid'),
        cls: this.getAverageMetric(metrics, 'cls'),
      },
      topEvents: this.getTopEvents(events),
      recentErrors: errors.slice(-5),
    };
  }

  private getAverageMetric(metrics: PerformanceMetric[], name: string): number {
    const filtered = metrics.filter(m => m.name === name);
    if (filtered.length === 0) return 0;
    return filtered.reduce((sum, m) => sum + m.value, 0) / filtered.length;
  }

  private getTopEvents(events: AnalyticsEvent[]): Array<{ name: string; count: number }> {
    const counts: Record<string, number> = {};
    events.forEach(event => {
      counts[event.name] = (counts[event.name] || 0) + 1;
    });
    
    return Object.entries(counts)
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  // Enable/disable analytics
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  // Clear all data
  clear() {
    this.events = [];
    this.performanceMetrics = [];
    this.errors = [];
    localStorage.removeItem('analytics_events');
    localStorage.removeItem('performance_metrics');
    localStorage.removeItem('error_events');
  }
}

// Create singleton instance
export const analytics = new Analytics();

// React hook for analytics
export function useAnalytics() {
  const track = React.useCallback((eventName: string, properties?: Record<string, any>) => {
    analytics.track(eventName, properties);
  }, []);

  const trackError = React.useCallback((error: Error, component?: string, metadata?: Record<string, any>) => {
    analytics.trackError(error, component, metadata);
  }, []);

  const trackPerformance = React.useCallback((metricName: string, value: number, metadata?: Record<string, any>) => {
    analytics.trackPerformance(metricName, value, metadata);
  }, []);

  return {
    track,
    trackError,
    trackPerformance,
    identify: analytics.identify.bind(analytics),
    page: analytics.page.bind(analytics),
  };
}
