import React, { memo, useMemo, useState, useCallback, Suspense } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { useDebounce } from 'use-debounce';
import { useAppStore } from '@/stores/appStore';
import { useContactSync } from '@/hooks/useAdvancedApi';
import { useCampaignProcessor } from '@/hooks/useWorker';
import { VirtualizedTable } from '@/components/ui/VirtualizedTable';
import { OptimizedButton } from '@/components/ui/OptimizedButton';
import { Card } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { 
  Users, 
  Search, 
  Upload, 
  PlusCircle, 
  RefreshCw, 
  Tag, 
  Edit, 
  Trash2,
  Download,
  Filter
} from 'lucide-react';
import type { Contact, Column } from '@/types';

// Lazy load heavy components
const ContactForm = React.lazy(() => import('@/components/forms/ContactForm'));
const BulkActions = React.lazy(() => import('@/components/contacts/BulkActions'));
const ContactImport = React.lazy(() => import('@/components/contacts/ContactImport'));

interface OptimizedContactsProps {
  onSwitchView: (view: string) => void;
}

// Memoized contact row component
const ContactRow = memo(({ 
  contact, 
  onEdit, 
  onDelete 
}: { 
  contact: Contact; 
  onEdit: (contact: Contact) => void;
  onDelete: (id: string) => void;
}) => (
  <div className="flex items-center justify-between p-3 hover:bg-gray-50">
    <div className="flex-1 min-w-0">
      <div className="flex items-center gap-3">
        <div className="flex-shrink-0">
          <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-blue-600">
              {contact.name.charAt(0).toUpperCase()}
            </span>
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 truncate">{contact.name}</p>
          <p className="text-sm text-gray-500 truncate">{contact.phone}</p>
        </div>
      </div>
    </div>
    <div className="flex items-center gap-2">
      <div className="flex flex-wrap gap-1">
        {contact.tags.slice(0, 2).map(tag => (
          <Badge key={tag} variant="secondary" size="sm">
            {tag}
          </Badge>
        ))}
        {contact.tags.length > 2 && (
          <Badge variant="outline" size="sm">
            +{contact.tags.length - 2}
          </Badge>
        )}
      </div>
      <OptimizedButton
        onClick={() => onEdit(contact)}
        variant="ghost"
        size="icon-sm"
        tooltip="Editar"
      >
        <Edit size={14} />
      </OptimizedButton>
      <OptimizedButton
        onClick={() => onDelete(contact.id as string)}
        variant="ghost"
        size="icon-sm"
        tooltip="Excluir"
      >
        <Trash2 size={14} />
      </OptimizedButton>
    </div>
  </div>
));

ContactRow.displayName = 'ContactRow';

const OptimizedContactsComponent: React.FC<OptimizedContactsProps> = ({ onSwitchView }) => {
  const contacts = useAppStore(state => state.contacts);
  const instances = useAppStore(state => state.instances);
  const settings = useAppStore(state => state.settings);
  const addContact = useAppStore(state => state.addContact);
  const updateContact = useAppStore(state => state.updateContact);
  const deleteContact = useAppStore(state => state.deleteContact);
  const setContacts = useAppStore(state => state.setContacts);

  const { syncContacts, isSyncing } = useContactSync();
  const { validatePhoneNumbers, parseCSV } = useCampaignProcessor();

  // Local state
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedContacts, setSelectedContacts] = useState<Set<string>>(new Set());
  const [showImport, setShowImport] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingContact, setEditingContact] = useState<Contact | null>(null);
  const [syncInstance, setSyncInstance] = useState(instances[0]?.name || '');

  // Debounced search
  const [debouncedSearchTerm] = useDebounce(searchTerm, 300);

  // Memoized filtered contacts
  const filteredContacts = useMemo(() => {
    return contacts.filter(contact => {
      // Search filter
      if (debouncedSearchTerm) {
        const searchLower = debouncedSearchTerm.toLowerCase();
        const matchesSearch = 
          contact.name.toLowerCase().includes(searchLower) ||
          contact.phone.includes(searchLower) ||
          contact.tags.some(tag => tag.toLowerCase().includes(searchLower));
        
        if (!matchesSearch) return false;
      }

      // Tags filter
      if (selectedTags.length > 0) {
        const hasMatchingTag = contact.tags.some(tag => selectedTags.includes(tag));
        if (!hasMatchingTag) return false;
      }

      return true;
    });
  }, [contacts, debouncedSearchTerm, selectedTags]);

  // Memoized table columns
  const columns: Column<Contact>[] = useMemo(() => [
    {
      key: 'name',
      header: 'Nome',
      sortable: true,
      render: (value, contact) => (
        <div className="flex items-center gap-3">
          <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
            <span className="text-xs font-medium text-blue-600">
              {contact.name.charAt(0).toUpperCase()}
            </span>
          </div>
          <span className="font-medium">{value}</span>
        </div>
      ),
    },
    {
      key: 'phone',
      header: 'Telefone',
      sortable: true,
      render: (value) => <span className="font-mono text-sm">{value}</span>,
    },
    {
      key: 'tags',
      header: 'Tags',
      render: (tags: string[]) => (
        <div className="flex flex-wrap gap-1">
          {tags.slice(0, 3).map(tag => (
            <Badge key={tag} variant="secondary" size="sm">
              {tag}
            </Badge>
          ))}
          {tags.length > 3 && (
            <Badge variant="outline" size="sm">
              +{tags.length - 3}
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Ações',
      render: (_, contact) => (
        <div className="flex items-center gap-1">
          <OptimizedButton
            onClick={() => handleEditContact(contact)}
            variant="ghost"
            size="icon-sm"
            tooltip="Editar"
          >
            <Edit size={14} />
          </OptimizedButton>
          <OptimizedButton
            onClick={() => handleDeleteContact(contact.id as string)}
            variant="ghost"
            size="icon-sm"
            tooltip="Excluir"
          >
            <Trash2 size={14} />
          </OptimizedButton>
        </div>
      ),
    },
  ], []);

  // Event handlers
  const handleSyncContacts = useCallback(async () => {
    if (!syncInstance) return;
    
    try {
      await syncContacts(syncInstance);
    } catch (error) {
      console.error('Sync failed:', error);
    }
  }, [syncContacts, syncInstance]);

  const handleEditContact = useCallback((contact: Contact) => {
    setEditingContact(contact);
    setShowForm(true);
  }, []);

  const handleDeleteContact = useCallback((id: string) => {
    deleteContact(id);
    setSelectedContacts(prev => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  }, [deleteContact]);

  const handleSaveContact = useCallback((contactData: Partial<Contact>) => {
    if (editingContact) {
      updateContact(editingContact.id as string, contactData);
    } else {
      const newContact: Contact = {
        id: Date.now().toString(),
        name: contactData.name || '',
        phone: contactData.phone || '',
        tags: contactData.tags || [],
        customFields: contactData.customFields || {},
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
      };
      addContact(newContact);
    }
    
    setShowForm(false);
    setEditingContact(null);
  }, [editingContact, updateContact, addContact]);

  const handleImportContacts = useCallback(async (file: File) => {
    try {
      const text = await file.text();
      const result = await parseCSV(text);
      
      if (result && result.data) {
        const newContacts: Contact[] = result.data.map((row: any, index: number) => ({
          id: `import-${Date.now()}-${index}`,
          name: row.name || row.Name || row.nome || '',
          phone: row.phone || row.Phone || row.telefone || '',
          tags: (row.tags || '').split(',').map((t: string) => t.trim()).filter(Boolean),
          customFields: { ...row },
          createdAt: new Date(),
          updatedAt: new Date(),
          isActive: true,
        }));

        setContacts([...contacts, ...newContacts]);
        setShowImport(false);
      }
    } catch (error) {
      console.error('Import failed:', error);
    }
  }, [contacts, setContacts, parseCSV]);

  // Get unique tags for filter
  const availableTags = useMemo(() => {
    const tagSet = new Set<string>();
    contacts.forEach(contact => {
      contact.tags.forEach(tag => tagSet.add(tag));
    });
    return Array.from(tagSet).sort();
  }, [contacts]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="bg-blue-100 p-2 rounded-lg">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-800">Gerenciador de Contatos</h2>
              <p className="text-sm text-gray-600">{filteredContacts.length} de {contacts.length} contatos</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <OptimizedButton
              onClick={() => setShowImport(true)}
              variant="outline"
              leftIcon={<Upload size={18} />}
            >
              Importar CSV
            </OptimizedButton>
            
            <OptimizedButton
              onClick={() => setShowForm(true)}
              variant="default"
              leftIcon={<PlusCircle size={18} />}
            >
              Adicionar Contato
            </OptimizedButton>
          </div>
        </div>

        {/* Sync Section */}
        <div className="bg-gray-50 border rounded-lg p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sincronizar da Instância
              </label>
              <select
                value={syncInstance}
                onChange={e => setSyncInstance(e.target.value)}
                disabled={instances.length === 0}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              >
                {instances.length === 0 ? (
                  <option>Nenhuma instância conectada</option>
                ) : (
                  instances.map(inst => (
                    <option key={inst.id} value={inst.name}>
                      {inst.name}
                    </option>
                  ))
                )}
              </select>
            </div>
            
            <OptimizedButton
              onClick={handleSyncContacts}
              disabled={isSyncing || instances.length === 0}
              loading={isSyncing}
              variant="success"
              fullWidth
              leftIcon={<RefreshCw size={18} />}
            >
              {isSyncing ? 'Sincronizando...' : 'Sincronizar Contatos'}
            </OptimizedButton>
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Buscar por nome, telefone ou tag..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div>
            <select
              multiple
              value={selectedTags}
              onChange={e => setSelectedTags(Array.from(e.target.selectedOptions, option => option.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 h-10"
            >
              {availableTags.map(tag => (
                <option key={tag} value={tag}>
                  {tag}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex items-center gap-2">
            {selectedTags.length > 0 && (
              <OptimizedButton
                onClick={() => setSelectedTags([])}
                variant="outline"
                size="sm"
              >
                Limpar Filtros
              </OptimizedButton>
            )}
          </div>
        </div>
      </Card>

      {/* Bulk Actions */}
      {selectedContacts.size > 0 && (
        <ErrorBoundary fallback={<div>Erro nas ações em lote</div>}>
          <Suspense fallback={<LoadingSpinner />}>
            <BulkActions
              selectedContacts={selectedContacts}
              onClearSelection={() => setSelectedContacts(new Set())}
            />
          </Suspense>
        </ErrorBoundary>
      )}

      {/* Contacts Table */}
      <Card>
        <VirtualizedTable
          data={filteredContacts}
          columns={columns}
          height={600}
          rowHeight={64}
          selectable
          selectedRows={selectedContacts}
          onSelectionChange={setSelectedContacts}
          getRowId={(contact) => contact.id as string}
          emptyMessage="Nenhum contato encontrado"
          loading={false}
          striped
          hoverable
        />
      </Card>

      {/* Modals */}
      {showForm && (
        <ErrorBoundary fallback={<div>Erro no formulário</div>}>
          <Suspense fallback={<LoadingSpinner />}>
            <ContactForm
              contact={editingContact}
              onSave={handleSaveContact}
              onClose={() => {
                setShowForm(false);
                setEditingContact(null);
              }}
            />
          </Suspense>
        </ErrorBoundary>
      )}

      {showImport && (
        <ErrorBoundary fallback={<div>Erro na importação</div>}>
          <Suspense fallback={<LoadingSpinner />}>
            <ContactImport
              onImport={handleImportContacts}
              onClose={() => setShowImport(false)}
            />
          </Suspense>
        </ErrorBoundary>
      )}
    </div>
  );
};

export const OptimizedContacts = memo(OptimizedContactsComponent);
