import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { 
  Settings, 
  Contact, 
  Group, 
  Template, 
  Instance, 
  Stats, 
  CampaignState, 
  ViewType,
  Message 
} from '@/types';

interface AppState {
  // UI State
  currentView: ViewType;
  loading: boolean;
  sidebarCollapsed: boolean;
  
  // Data State
  settings: Settings;
  contacts: Contact[];
  groups: Group[];
  templates: Template[];
  instances: Instance[];
  stats: Stats;
  allTags: string[];
  
  // Campaign State
  campaignState: CampaignState;
  messages: Message[];
  
  // Performance State
  performanceMetrics: {
    renderCount: number;
    lastRenderTime: number;
    memoryUsage: number;
  };
}

interface AppActions {
  // UI Actions
  setView: (view: ViewType) => void;
  setLoading: (loading: boolean) => void;
  toggleSidebar: () => void;
  
  // Settings Actions
  updateSettings: (settings: Partial<Settings>) => void;
  resetSettings: () => void;
  
  // Contacts Actions
  addContact: (contact: Contact) => void;
  updateContact: (id: string, contact: Partial<Contact>) => void;
  deleteContact: (id: string) => void;
  setContacts: (contacts: Contact[]) => void;
  bulkUpdateContacts: (updates: Array<{ id: string; data: Partial<Contact> }>) => void;
  
  // Groups Actions
  addGroup: (group: Group) => void;
  updateGroup: (id: string, group: Partial<Group>) => void;
  deleteGroup: (id: string) => void;
  setGroups: (groups: Group[]) => void;
  
  // Templates Actions
  addTemplate: (template: Template) => void;
  updateTemplate: (id: string, template: Partial<Template>) => void;
  deleteTemplate: (id: string) => void;
  setTemplates: (templates: Template[]) => void;
  
  // Instances Actions
  setInstances: (instances: Instance[]) => void;
  updateInstance: (id: string, instance: Partial<Instance>) => void;
  
  // Stats Actions
  updateStats: (stats: Partial<Stats>) => void;
  
  // Campaign Actions
  setCampaignState: (state: Partial<CampaignState>) => void;
  resetCampaign: () => void;
  
  // Messages Actions
  setMessages: (messages: Message[]) => void;
  addMessage: (message: Message) => void;
  updateMessage: (index: number, message: Partial<Message>) => void;
  deleteMessage: (index: number) => void;
  
  // Tags Actions
  addTag: (tag: string) => void;
  removeTag: (tag: string) => void;
  setAllTags: (tags: string[]) => void;
  
  // Performance Actions
  updatePerformanceMetrics: (metrics: Partial<AppState['performanceMetrics']>) => void;
  
  // Utility Actions
  reset: () => void;
}

const defaultSettings: Settings = {
  baseUrl: '',
  apiKey: '',
  minDelay: '2',
  maxDelay: '5',
  aiProvider: 'gemini',
  aiApiKey: '',
  systemPrompt: 'Você é um especialista em marketing para WhatsApp. Crie mensagens curtas, claras e persuasivas, usando formatação como negrito e itálico para destacar pontos importantes. A mensagem deve ser amigável e terminar com uma chamada para ação clara.'
};

const defaultStats: Stats = {
  total: 0,
  success: 0,
  errors: 0,
  lastRun: null
};

const defaultCampaignState: CampaignState = {
  isSending: false,
  progress: 0,
  success: 0,
  errors: [],
  current: 0,
  total: 0,
  message: '',
  config: null
};

const initialState: AppState = {
  currentView: 'dashboard',
  loading: false,
  sidebarCollapsed: false,
  settings: defaultSettings,
  contacts: [],
  groups: [],
  templates: [],
  instances: [],
  stats: defaultStats,
  allTags: [],
  campaignState: defaultCampaignState,
  messages: [{ id: Date.now(), text: '', mediaFile: null }],
  performanceMetrics: {
    renderCount: 0,
    lastRenderTime: 0,
    memoryUsage: 0
  }
};

export const useAppStore = create<AppState & AppActions>()(
  devtools(
    persist(
      subscribeWithSelector(
        immer((set, get) => ({
          ...initialState,
          
          // UI Actions
          setView: (view) => set((state) => { state.currentView = view; }),
          setLoading: (loading) => set((state) => { state.loading = loading; }),
          toggleSidebar: () => set((state) => { state.sidebarCollapsed = !state.sidebarCollapsed; }),
          
          // Settings Actions
          updateSettings: (newSettings) => set((state) => {
            Object.assign(state.settings, newSettings);
          }),
          resetSettings: () => set((state) => { state.settings = defaultSettings; }),
          
          // Contacts Actions
          addContact: (contact) => set((state) => {
            state.contacts.push(contact);
            // Update tags
            if (contact.tags) {
              contact.tags.forEach(tag => {
                if (!state.allTags.includes(tag)) {
                  state.allTags.push(tag);
                }
              });
            }
          }),
          
          updateContact: (id, contactData) => set((state) => {
            const index = state.contacts.findIndex(c => c.id === id);
            if (index !== -1) {
              Object.assign(state.contacts[index], contactData);
              // Update tags if changed
              if (contactData.tags) {
                contactData.tags.forEach(tag => {
                  if (!state.allTags.includes(tag)) {
                    state.allTags.push(tag);
                  }
                });
              }
            }
          }),
          
          deleteContact: (id) => set((state) => {
            state.contacts = state.contacts.filter(c => c.id !== id);
          }),
          
          setContacts: (contacts) => set((state) => {
            state.contacts = contacts;
            // Update all tags
            const allTags = new Set(state.allTags);
            contacts.forEach(contact => {
              if (contact.tags) {
                contact.tags.forEach(tag => allTags.add(tag));
              }
            });
            state.allTags = Array.from(allTags);
          }),
          
          bulkUpdateContacts: (updates) => set((state) => {
            updates.forEach(({ id, data }) => {
              const index = state.contacts.findIndex(c => c.id === id);
              if (index !== -1) {
                Object.assign(state.contacts[index], data);
              }
            });
          }),
          
          // Groups Actions
          addGroup: (group) => set((state) => { state.groups.push(group); }),
          updateGroup: (id, groupData) => set((state) => {
            const index = state.groups.findIndex(g => g.id === id);
            if (index !== -1) {
              Object.assign(state.groups[index], groupData);
            }
          }),
          deleteGroup: (id) => set((state) => {
            state.groups = state.groups.filter(g => g.id !== id);
          }),
          setGroups: (groups) => set((state) => { state.groups = groups; }),
          
          // Templates Actions
          addTemplate: (template) => set((state) => { state.templates.push(template); }),
          updateTemplate: (id, templateData) => set((state) => {
            const index = state.templates.findIndex(t => t.id === id);
            if (index !== -1) {
              Object.assign(state.templates[index], templateData);
            }
          }),
          deleteTemplate: (id) => set((state) => {
            state.templates = state.templates.filter(t => t.id !== id);
          }),
          setTemplates: (templates) => set((state) => { state.templates = templates; }),
          
          // Instances Actions
          setInstances: (instances) => set((state) => { state.instances = instances; }),
          updateInstance: (id, instanceData) => set((state) => {
            const index = state.instances.findIndex(i => i.id === id);
            if (index !== -1) {
              Object.assign(state.instances[index], instanceData);
            }
          }),
          
          // Stats Actions
          updateStats: (newStats) => set((state) => {
            Object.assign(state.stats, newStats);
          }),
          
          // Campaign Actions
          setCampaignState: (newState) => set((state) => {
            Object.assign(state.campaignState, newState);
          }),
          resetCampaign: () => set((state) => { state.campaignState = defaultCampaignState; }),
          
          // Messages Actions
          setMessages: (messages) => set((state) => { state.messages = messages; }),
          addMessage: (message) => set((state) => { state.messages.push(message); }),
          updateMessage: (index, messageData) => set((state) => {
            if (state.messages[index]) {
              Object.assign(state.messages[index], messageData);
            }
          }),
          deleteMessage: (index) => set((state) => {
            if (state.messages.length > 1) {
              state.messages.splice(index, 1);
            }
          }),
          
          // Tags Actions
          addTag: (tag) => set((state) => {
            if (!state.allTags.includes(tag)) {
              state.allTags.push(tag);
            }
          }),
          removeTag: (tag) => set((state) => {
            state.allTags = state.allTags.filter(t => t !== tag);
          }),
          setAllTags: (tags) => set((state) => { state.allTags = tags; }),
          
          // Performance Actions
          updatePerformanceMetrics: (metrics) => set((state) => {
            Object.assign(state.performanceMetrics, metrics);
          }),
          
          // Utility Actions
          reset: () => set(() => initialState),
        }))
      ),
      {
        name: 'whatsapp-sender-store',
        partialize: (state) => ({
          settings: state.settings,
          contacts: state.contacts,
          groups: state.groups,
          templates: state.templates,
          stats: state.stats,
          allTags: state.allTags,
          messages: state.messages,
          sidebarCollapsed: state.sidebarCollapsed,
        }),
      }
    ),
    { name: 'WhatsApp Sender Store' }
  )
);

// Selectors for optimized re-renders
export const useSettings = () => useAppStore((state) => state.settings);
export const useContacts = () => useAppStore((state) => state.contacts);
export const useGroups = () => useAppStore((state) => state.groups);
export const useTemplates = () => useAppStore((state) => state.templates);
export const useInstances = () => useAppStore((state) => state.instances);
export const useStats = () => useAppStore((state) => state.stats);
export const useCampaignState = () => useAppStore((state) => state.campaignState);
export const useMessages = () => useAppStore((state) => state.messages);
export const useCurrentView = () => useAppStore((state) => state.currentView);
export const useLoading = () => useAppStore((state) => state.loading);
export const useAllTags = () => useAppStore((state) => state.allTags);
export const usePerformanceMetrics = () => useAppStore((state) => state.performanceMetrics);
