// Utilitário para testar configuração da API

interface ApiTestResult {
  success: boolean;
  message: string;
  details?: any;
  endpoint?: string;
}

interface ApiConfig {
  baseUrl: string;
  apiKey: string;
}

export class ApiTester {
  private config: ApiConfig;

  constructor(config: ApiConfig) {
    this.config = config;
  }

  // Teste básico de conectividade
  async testConnection(): Promise<ApiTestResult> {
    try {
      if (!this.config.baseUrl) {
        return {
          success: false,
          message: 'URL Base da API não configurada',
          endpoint: 'N/A'
        };
      }

      if (!this.config.apiKey) {
        return {
          success: false,
          message: 'API Key não configurada',
          endpoint: 'N/A'
        };
      }

      const endpoint = '/instance/fetchInstances';
      const url = `${this.config.baseUrl.replace(/\/$/, '')}${endpoint}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'apikey': this.config.apiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        return {
          success: false,
          message: `Erro HTTP ${response.status}: ${response.statusText}`,
          details: errorText,
          endpoint
        };
      }

      const data = await response.json();
      
      return {
        success: true,
        message: 'Conexão com API estabelecida com sucesso!',
        details: data,
        endpoint
      };

    } catch (error: any) {
      return {
        success: false,
        message: `Erro de conexão: ${error.message}`,
        details: error,
        endpoint: '/instance/fetchInstances'
      };
    }
  }

  // Teste de criação de instância
  async testCreateInstance(instanceName: string = 'test_instance'): Promise<ApiTestResult> {
    try {
      const endpoint = '/instance/create';
      const url = `${this.config.baseUrl.replace(/\/$/, '')}${endpoint}`;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'apikey': this.config.apiKey,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          instanceName,
          qrcode: true
        })
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          message: `Erro ao criar instância: ${data.message || response.statusText}`,
          details: data,
          endpoint
        };
      }

      return {
        success: true,
        message: `Instância '${instanceName}' criada com sucesso!`,
        details: data,
        endpoint
      };

    } catch (error: any) {
      return {
        success: false,
        message: `Erro ao criar instância: ${error.message}`,
        details: error,
        endpoint: '/instance/create'
      };
    }
  }

  // Teste de status de instância
  async testInstanceStatus(instanceName: string): Promise<ApiTestResult> {
    try {
      const endpoint = `/instance/connectionState/${instanceName}`;
      const url = `${this.config.baseUrl.replace(/\/$/, '')}${endpoint}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'apikey': this.config.apiKey,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          message: `Erro ao verificar status: ${data.message || response.statusText}`,
          details: data,
          endpoint
        };
      }

      const isConnected = data.state === 'open' || data.instance?.state === 'open';

      return {
        success: true,
        message: `Status da instância '${instanceName}': ${isConnected ? 'Conectada' : 'Desconectada'}`,
        details: data,
        endpoint
      };

    } catch (error: any) {
      return {
        success: false,
        message: `Erro ao verificar status: ${error.message}`,
        details: error,
        endpoint: `/instance/connectionState/${instanceName}`
      };
    }
  }

  // Teste de envio de mensagem
  async testSendMessage(instanceName: string, phoneNumber: string, message: string = 'Teste de API'): Promise<ApiTestResult> {
    try {
      const endpoint = `/message/sendText/${instanceName}`;
      const url = `${this.config.baseUrl.replace(/\/$/, '')}${endpoint}`;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'apikey': this.config.apiKey,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          number: phoneNumber,
          text: message,
          options: {
            delay: 1000
          }
        })
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          message: `Erro ao enviar mensagem: ${data.message || response.statusText}`,
          details: data,
          endpoint
        };
      }

      return {
        success: true,
        message: `Mensagem enviada com sucesso para ${phoneNumber}!`,
        details: data,
        endpoint
      };

    } catch (error: any) {
      return {
        success: false,
        message: `Erro ao enviar mensagem: ${error.message}`,
        details: error,
        endpoint: `/message/sendText/${instanceName}`
      };
    }
  }

  // Teste completo da API
  async runFullTest(instanceName: string = 'test_instance'): Promise<ApiTestResult[]> {
    const results: ApiTestResult[] = [];

    console.log('🔍 Iniciando testes da API...');

    // 1. Teste de conexão
    console.log('1. Testando conexão...');
    const connectionTest = await this.testConnection();
    results.push(connectionTest);

    if (!connectionTest.success) {
      console.log('❌ Falha na conexão. Parando testes.');
      return results;
    }

    // 2. Teste de criação de instância
    console.log('2. Testando criação de instância...');
    const createTest = await this.testCreateInstance(instanceName);
    results.push(createTest);

    // 3. Teste de status (independente do resultado da criação)
    console.log('3. Testando status da instância...');
    const statusTest = await this.testInstanceStatus(instanceName);
    results.push(statusTest);

    console.log('✅ Testes concluídos!');
    return results;
  }

  // Diagnóstico da configuração
  async diagnose(): Promise<{
    config: any;
    tests: ApiTestResult[];
    recommendations: string[];
  }> {
    const recommendations: string[] = [];

    // Verificar configuração
    if (!this.config.baseUrl) {
      recommendations.push('Configure a URL Base da API em Configurações');
    } else if (!this.config.baseUrl.startsWith('http')) {
      recommendations.push('URL Base deve começar com http:// ou https://');
    }

    if (!this.config.apiKey) {
      recommendations.push('Configure a API Key em Configurações');
    } else if (this.config.apiKey.length < 10) {
      recommendations.push('API Key parece muito curta. Verifique se está correta');
    }

    // Executar testes
    const tests = await this.runFullTest();

    // Analisar resultados e adicionar recomendações
    const connectionTest = tests.find(t => t.endpoint === '/instance/fetchInstances');
    if (connectionTest && !connectionTest.success) {
      if (connectionTest.message.includes('401') || connectionTest.message.includes('Unauthorized')) {
        recommendations.push('API Key inválida. Verifique se está correta');
      } else if (connectionTest.message.includes('404')) {
        recommendations.push('Endpoint não encontrado. Verifique se a URL da API está correta');
      } else if (connectionTest.message.includes('ECONNREFUSED')) {
        recommendations.push('Não foi possível conectar. Verifique se a API está rodando');
      }
    }

    return {
      config: {
        baseUrl: this.config.baseUrl,
        apiKeyConfigured: !!this.config.apiKey,
        apiKeyLength: this.config.apiKey?.length || 0
      },
      tests,
      recommendations
    };
  }
}

// Hook para usar o testador de API
export const useApiTester = () => {
  const testApi = async (config: ApiConfig) => {
    const tester = new ApiTester(config);
    return await tester.diagnose();
  };

  const quickTest = async (config: ApiConfig) => {
    const tester = new ApiTester(config);
    return await tester.testConnection();
  };

  return {
    testApi,
    quickTest
  };
};

// Função utilitária para testar configuração atual
export const testCurrentConfig = async () => {
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';
  const apiKey = process.env.NEXT_PUBLIC_API_KEY || '';

  const tester = new ApiTester({ baseUrl, apiKey });
  return await tester.diagnose();
};

// Validador de configuração
export const validateApiConfig = (config: ApiConfig): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!config.baseUrl) {
    errors.push('URL Base da API é obrigatória');
  } else {
    try {
      new URL(config.baseUrl);
    } catch {
      errors.push('URL Base da API é inválida');
    }
  }

  if (!config.apiKey) {
    errors.push('API Key é obrigatória');
  } else if (config.apiKey.length < 8) {
    errors.push('API Key deve ter pelo menos 8 caracteres');
  }

  return {
    valid: errors.length === 0,
    errors
  };
};
