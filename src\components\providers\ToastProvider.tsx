import React, { createContext, useContext } from 'react';
import { useNotifications } from '../../hooks/useNotifications';
import { ToastContainer } from '../ui/Toast';

interface ToastContextType {
  addNotification: (message: string, type?: 'success' | 'error' | 'info') => void;
  removeNotification: (id: string | number) => void;
  clearAllNotifications: () => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { notifications, addNotification, removeNotification, clearAllNotifications } = useNotifications();

  const value: ToastContextType = {
    addNotification,
    removeNotification,
    clearAllNotifications,
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer 
        notifications={notifications} 
        removeNotification={removeNotification} 
      />
    </ToastContext.Provider>
  );
};

export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};
