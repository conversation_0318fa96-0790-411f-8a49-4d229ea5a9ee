import { useState, useCallback } from 'react';
import { useToast } from '../components/providers/ToastProvider';
import { handleApiError, safeAsync, withRetry, AppError } from '../utils/errorHandling';

interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: AppError | null;
}

interface UseApiOptions {
  showErrorToast?: boolean;
  showSuccessToast?: boolean;
  successMessage?: string;
  retries?: number;
  retryDelay?: number;
}

export const useApi = <T = any>(options: UseApiOptions = {}) => {
  const {
    showErrorToast = true,
    showSuccessToast = false,
    successMessage,
    retries = 0,
    retryDelay = 1000,
  } = options;

  const { addNotification } = useToast();
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(async (
    apiCall: () => Promise<T>,
    customOptions?: Partial<UseApiOptions>
  ): Promise<{ data?: T; error?: AppError }> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    const finalOptions = { ...options, ...customOptions };

    try {
      const operation = async () => {
        try {
          return await apiCall();
        } catch (error) {
          throw handleApiError(error);
        }
      };

      const data = retries > 0 
        ? await withRetry(operation, retries, retryDelay)
        : await operation();

      setState({ data, loading: false, error: null });

      if (finalOptions.showSuccessToast && finalOptions.successMessage) {
        addNotification(finalOptions.successMessage, 'success');
      }

      return { data };
    } catch (error) {
      const appError = error instanceof AppError ? error : handleApiError(error);
      
      setState(prev => ({ ...prev, loading: false, error: appError }));

      if (finalOptions.showErrorToast) {
        addNotification(appError.message, 'error');
      }

      return { error: appError };
    }
  }, [addNotification, options, retries, retryDelay]);

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null });
  }, []);

  return {
    ...state,
    execute,
    reset,
  };
};

// Specialized hook for WhatsApp API calls
export const useWhatsAppApi = () => {
  const { addNotification } = useToast();

  const makeApiCall = useCallback(async (
    baseUrl: string,
    apiKey: string,
    endpoint: string,
    options: RequestInit = {}
  ) => {
    if (!baseUrl || !apiKey) {
      throw new AppError('URL Base e API Key devem ser configuradas.', 'MISSING_CONFIG');
    }

    const fullUrl = `${baseUrl.replace(/\/$/, '')}/${endpoint.replace(/^\//, '')}`;
    
    const response = await fetch(fullUrl, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'apikey': apiKey,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new AppError(
        errorData.message || errorData.error || `Erro ${response.status}`,
        'API_ERROR',
        response.status
      );
    }

    return response.json();
  }, []);

  return { makeApiCall };
};

// Hook for handling form submissions with error handling
export const useFormSubmit = <T = any>() => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addNotification } = useToast();

  const submit = useCallback(async (
    submitFn: () => Promise<T>,
    options: {
      successMessage?: string;
      errorMessage?: string;
      onSuccess?: (data: T) => void;
      onError?: (error: AppError) => void;
    } = {}
  ) => {
    setIsSubmitting(true);

    const { data, error } = await safeAsync(submitFn);

    setIsSubmitting(false);

    if (error) {
      const message = options.errorMessage || error.message;
      addNotification(message, 'error');
      options.onError?.(error);
      return { error };
    }

    if (options.successMessage) {
      addNotification(options.successMessage, 'success');
    }

    options.onSuccess?.(data!);
    return { data };
  }, [addNotification]);

  return {
    isSubmitting,
    submit,
  };
};
