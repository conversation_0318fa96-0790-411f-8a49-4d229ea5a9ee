# 🚀 Guia de Deploy - WhatsApp Sender Otimizado

## 📋 Pré-requisitos

### Ambiente de Desenvolvimento
- Node.js 18+ 
- npm 8+ ou yarn 1.22+
- Git

### Ambiente de Produção
- Servidor com Node.js 18+
- <PERSON>inx (recomendado)
- SSL Certificate
- Domínio configurado

## 🔧 Configuração Local

### 1. Clone e Instalação
```bash
# Clone o repositório
git clone https://github.com/whatsapp-sender-optimized.git
cd whatsapp-sender-optimized

# Instale as dependências
npm install

# Configure as variáveis de ambiente
cp .env.example .env.local
```

### 2. Configuração das Variáveis
Edite o arquivo `.env.local`:

```bash
# API Configuration
NEXT_PUBLIC_API_BASE_URL="https://sua-api-whatsapp.com"

# Analytics (opcional)
NEXT_PUBLIC_ANALYTICS_ENABLED=true

# Feature Flags
NEXT_PUBLIC_ENABLE_WEB_WORKERS=true
NEXT_PUBLIC_ENABLE_VIRTUAL_SCROLLING=true
```

### 3. Desenvolvimento
```bash
# Inicie o servidor de desenvolvimento
npm run dev

# Acesse http://localhost:3000
```

## 🌐 Deploy em Produção

### Opção 1: Vercel (Recomendado)

#### Deploy Automático
1. Conecte seu repositório ao Vercel
2. Configure as variáveis de ambiente
3. Deploy automático a cada push

#### Deploy Manual
```bash
# Instale a CLI do Vercel
npm i -g vercel

# Faça login
vercel login

# Deploy
vercel --prod
```

#### Configuração no Vercel
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "framework": "nextjs",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  }
}
```

### Opção 2: Netlify

#### netlify.toml
```toml
[build]
  command = "npm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### Opção 3: Docker

#### Dockerfile
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

#### docker-compose.yml
```yaml
version: '3.8'
services:
  whatsapp-sender:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=${API_BASE_URL}
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - whatsapp-sender
    restart: unless-stopped
```

### Opção 4: VPS/Servidor Próprio

#### 1. Preparação do Servidor
```bash
# Atualize o sistema
sudo apt update && sudo apt upgrade -y

# Instale Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Instale PM2 para gerenciamento de processos
sudo npm install -g pm2

# Instale Nginx
sudo apt install nginx -y
```

#### 2. Deploy da Aplicação
```bash
# Clone o projeto
git clone https://github.com/whatsapp-sender-optimized.git
cd whatsapp-sender-optimized

# Instale dependências
npm ci

# Configure variáveis de ambiente
cp .env.example .env.local
# Edite .env.local com suas configurações

# Build para produção
npm run build

# Inicie com PM2
pm2 start npm --name "whatsapp-sender" -- start
pm2 save
pm2 startup
```

#### 3. Configuração do Nginx
```nginx
server {
    listen 80;
    server_name seu-dominio.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name seu-dominio.com;
    
    # SSL Configuration
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Cache static assets
    location /_next/static/ {
        alias /path/to/app/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 🔒 Configurações de Segurança

### 1. Variáveis de Ambiente Seguras
```bash
# Nunca commite arquivos .env
echo ".env*" >> .gitignore

# Use secrets management em produção
# Vercel: Environment Variables
# AWS: Systems Manager Parameter Store
# Azure: Key Vault
```

### 2. Headers de Segurança
```javascript
// next.config.js
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'SAMEORIGIN'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
];

module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
    ];
  },
};
```

## 📊 Monitoramento

### 1. Health Check
```javascript
// pages/api/health.js
export default function handler(req, res) {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.NEXT_PUBLIC_APP_VERSION
  });
}
```

### 2. Logs
```bash
# PM2 logs
pm2 logs whatsapp-sender

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 3. Métricas
```bash
# PM2 monitoring
pm2 monit

# Sistema
htop
df -h
free -m
```

## 🔄 CI/CD

### GitHub Actions
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Build
      run: npm run build
    
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v20
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        vercel-args: '--prod'
```

## 🚨 Troubleshooting

### Problemas Comuns

#### 1. Build Falha
```bash
# Limpe cache e reinstale
rm -rf .next node_modules package-lock.json
npm install
npm run build
```

#### 2. Erro de Memória
```bash
# Aumente o limite de memória do Node.js
NODE_OPTIONS="--max-old-space-size=4096" npm run build
```

#### 3. Problemas de SSL
```bash
# Verifique certificados
openssl x509 -in certificate.crt -text -noout
```

### Logs Úteis
```bash
# Next.js debug
DEBUG=* npm run dev

# PM2 restart
pm2 restart whatsapp-sender

# Nginx test
sudo nginx -t
sudo systemctl reload nginx
```

## 📞 Suporte

Para problemas de deploy:
1. Verifique os logs primeiro
2. Consulte a documentação oficial
3. Abra uma issue no repositório
4. Entre em contato com o suporte

---

**✅ Deploy realizado com sucesso!**
