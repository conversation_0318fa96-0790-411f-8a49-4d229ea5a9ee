import React, { memo, useMemo, Suspense } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { useAppStore } from '@/stores/appStore';
import { useInstancesQuery } from '@/hooks/useAdvancedApi';
import { useCampaign } from '@/hooks/useCampaign';
import { OptimizedButton } from '@/components/ui/OptimizedButton';
import { Card } from '@/components/ui/Card';
import { ProgressBar } from '@/components/ui/ProgressBar';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { 
  LayoutDashboard, 
  Users, 
  Server, 
  Target, 
  Activity, 
  Clock, 
  Play, 
  StopCircle,
  TrendingUp,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

// Lazy load heavy components
const CampaignChart = React.lazy(() => import('@/components/charts/CampaignChart'));
const PerformanceMetrics = React.lazy(() => import('@/components/metrics/PerformanceMetrics'));

interface DashboardProps {
  onSwitchView: (view: string) => void;
}

// Memoized stat card component
const StatCard = memo(({ 
  icon: Icon, 
  title, 
  value, 
  subtitle, 
  trend, 
  color = 'blue' 
}: {
  icon: React.ComponentType<any>;
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: { value: number; isPositive: boolean };
  color?: 'blue' | 'green' | 'red' | 'yellow';
}) => {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600',
    green: 'bg-green-100 text-green-600',
    red: 'bg-red-100 text-red-600',
    yellow: 'bg-yellow-100 text-yellow-600',
  };

  return (
    <Card className="p-6 hover:shadow-lg transition-shadow duration-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mt-1">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
          )}
          {trend && (
            <div className={`flex items-center mt-2 text-sm ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
              <TrendingUp className={`h-4 w-4 mr-1 ${!trend.isPositive && 'rotate-180'}`} />
              {Math.abs(trend.value)}% vs último mês
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
          <Icon className="h-6 w-6" />
        </div>
      </div>
    </Card>
  );
});

StatCard.displayName = 'StatCard';

// Memoized campaign status component
const CampaignStatus = memo(() => {
  const campaignState = useAppStore(state => state.campaignState);
  const { resumeCampaign, cancelCampaign, pauseCampaign } = useCampaign();

  const hasActiveCampaign = campaignState.total > 0;
  const isPaused = hasActiveCampaign && !campaignState.isSending;

  if (!hasActiveCampaign) return null;

  return (
    <Card className={`p-6 ${isPaused ? 'bg-yellow-50 border-yellow-200' : 'bg-blue-50 border-blue-200'}`}>
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-4">
            <div className={`p-2 rounded-lg ${isPaused ? 'bg-yellow-100' : 'bg-blue-100'}`}>
              {isPaused ? (
                <Clock className="h-5 w-5 text-yellow-600" />
              ) : (
                <Activity className="h-5 w-5 text-blue-600" />
              )}
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">
                {isPaused ? 'Campanha Pausada' : 'Campanha em Andamento'}
              </h3>
              <p className="text-sm text-gray-600">
                {isPaused 
                  ? `Faltam ${campaignState.total - campaignState.current} de ${campaignState.total} destinatários.`
                  : `Enviando para ${campaignState.current} de ${campaignState.total}...`
                }
              </p>
            </div>
          </div>
          
          <ProgressBar 
            progress={campaignState.progress} 
            className="mb-4"
            showPercentage
          />
          
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{campaignState.success}</p>
              <p className="text-sm text-gray-500">Sucessos</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">{campaignState.errors.length}</p>
              <p className="text-sm text-gray-500">Falhas</p>
            </div>
          </div>
        </div>
        
        <div className="flex gap-2 ml-4">
          {isPaused && (
            <OptimizedButton
              onClick={resumeCampaign}
              variant="success"
              size="sm"
              leftIcon={<Play size={16} />}
              tooltip="Continuar Campanha"
            >
              Continuar
            </OptimizedButton>
          )}
          <OptimizedButton
            onClick={campaignState.isSending ? pauseCampaign : cancelCampaign}
            variant="destructive"
            size="sm"
            leftIcon={<StopCircle size={16} />}
            tooltip={campaignState.isSending ? "Pausar Campanha" : "Cancelar Campanha"}
          >
            {campaignState.isSending ? 'Pausar' : 'Cancelar'}
          </OptimizedButton>
        </div>
      </div>
    </Card>
  );
});

CampaignStatus.displayName = 'CampaignStatus';

// Main dashboard component
const OptimizedDashboardComponent: React.FC<DashboardProps> = ({ onSwitchView }) => {
  const stats = useAppStore(state => state.stats);
  const contacts = useAppStore(state => state.contacts);
  const groups = useAppStore(state => state.groups);
  const { instances, isLoading: loadingInstances } = useInstancesQuery();

  // Memoized calculations
  const dashboardStats = useMemo(() => {
    const connectedInstances = instances.filter(inst => inst.connectionStatus === 'open').length;
    const totalContacts = contacts.length;
    const totalGroups = groups.length;
    const activeContacts = contacts.filter(c => c.isActive !== false).length;

    return {
      connectedInstances,
      totalInstances: instances.length,
      totalContacts,
      activeContacts,
      totalGroups,
      lastCampaignStats: stats,
    };
  }, [instances, contacts, groups, stats]);

  // Error fallback component
  const ErrorFallback = ({ error, resetErrorBoundary }: any) => (
    <Card className="p-6 border-red-200 bg-red-50">
      <div className="flex items-center gap-3 text-red-800">
        <AlertCircle className="h-5 w-5" />
        <div>
          <h3 className="font-semibold">Erro no Dashboard</h3>
          <p className="text-sm mt-1">{error.message}</p>
          <OptimizedButton
            onClick={resetErrorBoundary}
            variant="outline"
            size="sm"
            className="mt-2"
          >
            Tentar Novamente
          </OptimizedButton>
        </div>
      </div>
    </Card>
  );

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Campaign Status */}
      <ErrorBoundary FallbackComponent={ErrorFallback}>
        <CampaignStatus />
      </ErrorBoundary>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          icon={Server}
          title="Instâncias"
          value={`${dashboardStats.connectedInstances}/${dashboardStats.totalInstances}`}
          subtitle="Conectadas"
          color="blue"
          trend={{ value: 5, isPositive: true }}
        />
        
        <StatCard
          icon={Users}
          title="Contatos"
          value={dashboardStats.totalContacts}
          subtitle={`${dashboardStats.activeContacts} ativos`}
          color="green"
          trend={{ value: 12, isPositive: true }}
        />
        
        <StatCard
          icon={Users}
          title="Grupos"
          value={dashboardStats.totalGroups}
          subtitle="Sincronizados"
          color="yellow"
        />
        
        <StatCard
          icon={Target}
          title="Última Campanha"
          value={`${dashboardStats.lastCampaignStats.success}/${dashboardStats.lastCampaignStats.total}`}
          subtitle={dashboardStats.lastCampaignStats.lastRun ? `Em ${new Date(dashboardStats.lastCampaignStats.lastRun).toLocaleDateString()}` : 'Nenhuma campanha'}
          color={dashboardStats.lastCampaignStats.errors > 0 ? 'red' : 'green'}
        />
      </div>

      {/* Charts and Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <Suspense fallback={<Card className="p-6"><LoadingSpinner /></Card>}>
            <CampaignChart />
          </Suspense>
        </ErrorBoundary>
        
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <Suspense fallback={<Card className="p-6"><LoadingSpinner /></Card>}>
            <PerformanceMetrics />
          </Suspense>
        </ErrorBoundary>
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Ações Rápidas</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <OptimizedButton
            onClick={() => onSwitchView('main')}
            variant="default"
            fullWidth
            leftIcon={<LayoutDashboard size={20} />}
          >
            Nova Campanha
          </OptimizedButton>
          
          <OptimizedButton
            onClick={() => onSwitchView('contacts')}
            variant="outline"
            fullWidth
            leftIcon={<Users size={20} />}
          >
            Gerenciar Contatos
          </OptimizedButton>
          
          <OptimizedButton
            onClick={() => onSwitchView('instances')}
            variant="outline"
            fullWidth
            leftIcon={<Server size={20} />}
          >
            Configurar Instâncias
          </OptimizedButton>
        </div>
      </Card>

      {/* System Status */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Status do Sistema</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">API Connection</span>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-600">Conectado</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Cache Status</span>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-600">Ativo</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Performance</span>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-600">Otimizado</span>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export const OptimizedDashboard = memo(OptimizedDashboardComponent);
