<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disparador WhatsApp em Massa - Versão Refatorada</title>
    
    <!-- CDN Dependencies -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://unpkg.com/papaparse@5.3.2/papaparse.min.js"></script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* Estilos customizados */
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #374151;
            background-color: #f9fafb;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            line-height: 1.3;
        }

        /* Animações */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { 
                transform: translateY(20px); 
                opacity: 0; 
            }
            to { 
                transform: translateY(0); 
                opacity: 1; 
            }
        }

        @keyframes slideDown {
            from { 
                transform: translateY(-20px); 
                opacity: 0; 
            }
            to { 
                transform: translateY(0); 
                opacity: 1; 
            }
        }

        .animate-fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        .animate-slide-up {
            animation: slideUp 0.3s ease-out;
        }

        .animate-slide-down {
            animation: slideDown 0.3s ease-out;
        }

        /* Scrollbar personalizada */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .text-shadow {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>

    <script>
        // Configuração do Tailwind
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        heading: ['Poppins', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        // Vou criar uma versão simplificada que funciona no navegador
        // Esta é uma demonstração das melhorias implementadas
        
        const { useState, useEffect, useCallback, useMemo, createContext, useContext } = React;
        const { 
            LayoutDashboard, 
            Send, 
            Users, 
            Server, 
            Bookmark, 
            Settings: SettingsIcon,
            MessageSquare,
            Upload,
            CheckCircle,
            AlertCircle,
            X,
            RefreshCw,
            PlusCircle
        } = lucide;

        // Simulação dos componentes refatorados
        
        // Button Component
        const Button = ({ children, variant = 'primary', size = 'md', loading = false, icon, fullWidth = false, className = '', ...props }) => {
            const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
            
            const variantClasses = {
                primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm hover:shadow-md',
                secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-500',
                success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-sm hover:shadow-md',
                danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm hover:shadow-md',
            };
            
            const sizeClasses = {
                sm: 'px-3 py-1.5 text-sm gap-1.5',
                md: 'px-4 py-2 text-sm gap-2',
                lg: 'px-6 py-3 text-base gap-2',
            };
            
            const widthClass = fullWidth ? 'w-full' : '';
            const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${widthClass} ${className}`;
            
            return React.createElement('button', {
                className: classes,
                disabled: loading,
                ...props
            }, 
                loading ? React.createElement('div', { className: 'animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full' }) : icon,
                children
            );
        };

        // Card Component
        const Card = ({ children, className = '', padding = 'md' }) => {
            const paddingClasses = {
                sm: 'p-4',
                md: 'p-6',
                lg: 'p-8',
            };
            
            return React.createElement('div', {
                className: `bg-white border border-gray-200 rounded-xl shadow-sm ${paddingClasses[padding]} ${className}`
            }, children);
        };

        // Toast Component
        const Toast = ({ message, type, onClose }) => {
            const styles = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                info: 'bg-blue-500',
            };
            
            const icons = {
                success: React.createElement(CheckCircle, { className: 'h-6 w-6' }),
                error: React.createElement(AlertCircle, { className: 'h-6 w-6' }),
                info: React.createElement(AlertCircle, { className: 'h-6 w-6' }),
            };

            useEffect(() => {
                const timer = setTimeout(() => {
                    onClose();
                }, 5000);
                return () => clearTimeout(timer);
            }, [onClose]);

            return React.createElement('div', {
                className: `relative flex items-center gap-4 w-full p-4 text-white rounded-lg shadow-lg animate-slide-down ${styles[type] || styles.info}`
            },
                React.createElement('div', {}, icons[type]),
                React.createElement('p', { className: 'flex-1 text-sm font-medium' }, message),
                React.createElement('button', {
                    onClick: onClose,
                    className: 'p-1 rounded-full hover:bg-white/20'
                }, React.createElement(X, { size: 18 }))
            );
        };

        // Context para notificações
        const NotificationContext = createContext();

        const NotificationProvider = ({ children }) => {
            const [notifications, setNotifications] = useState([]);

            const addNotification = useCallback((message, type = 'info') => {
                const id = Date.now() + Math.random();
                setNotifications(current => [...current, { id, message, type }]);
            }, []);

            const removeNotification = useCallback((id) => {
                setNotifications(current => current.filter(n => n.id !== id));
            }, []);

            return React.createElement(NotificationContext.Provider, {
                value: { addNotification, removeNotification }
            },
                children,
                React.createElement('div', {
                    className: 'fixed top-5 right-5 z-50 w-full max-w-sm space-y-3'
                },
                    notifications.map(notification =>
                        React.createElement(Toast, {
                            key: notification.id,
                            message: notification.message,
                            type: notification.type,
                            onClose: () => removeNotification(notification.id)
                        })
                    )
                )
            );
        };

        const useNotification = () => {
            const context = useContext(NotificationContext);
            if (!context) {
                throw new Error('useNotification must be used within NotificationProvider');
            }
            return context;
        };

        // Header Component
        const Header = ({ currentView, setView }) => {
            const NavButton = ({ targetView, icon: Icon, label }) => {
                const isActive = currentView === targetView;
                
                return React.createElement('button', {
                    onClick: () => setView(targetView),
                    className: `flex-1 md:flex-initial flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                        isActive 
                            ? 'bg-white text-blue-700 shadow-sm' 
                            : 'text-gray-500 hover:bg-gray-200 hover:text-gray-700'
                    }`
                },
                    React.createElement(Icon, { size: 18 }),
                    React.createElement('span', {}, label)
                );
            };

            return React.createElement('header', {
                className: 'flex flex-col md:flex-row items-center justify-between mb-6 gap-4'
            },
                React.createElement('h1', {
                    className: 'text-3xl font-bold text-gray-900 font-heading text-shadow'
                }, 'Disparador em Massa'),
                
                React.createElement('nav', { className: 'w-full md:w-auto' },
                    React.createElement('div', {
                        className: 'flex items-center justify-center gap-1 p-1 bg-gray-100 rounded-xl flex-wrap shadow-sm'
                    },
                        React.createElement(NavButton, { targetView: 'dashboard', icon: LayoutDashboard, label: 'Dashboard' }),
                        React.createElement(NavButton, { targetView: 'main', icon: Send, label: 'Disparador' }),
                        React.createElement(NavButton, { targetView: 'contacts', icon: Users, label: 'Contatos' }),
                        React.createElement(NavButton, { targetView: 'instances', icon: Server, label: 'Instâncias' }),
                        React.createElement(NavButton, { targetView: 'templates', icon: Bookmark, label: 'Templates' }),
                        React.createElement('button', {
                            onClick: () => setView('settings'),
                            className: `p-2.5 rounded-lg transition-all duration-200 ${
                                currentView === 'settings' 
                                    ? 'bg-white text-blue-600 shadow-sm' 
                                    : 'text-gray-500 hover:bg-gray-200 hover:text-gray-700'
                            }`
                        }, React.createElement(SettingsIcon, { size: 18 }))
                    )
                )
            );
        };

        // Dashboard View
        const DashboardView = () => {
            const { addNotification } = useNotification();

            const stats = [
                { icon: Server, title: 'Instâncias', value: '2 / 3', subtitle: 'Conectadas', color: 'text-blue-600' },
                { icon: Users, title: 'Contatos', value: '1,234', subtitle: 'Salvos na base', color: 'text-green-600' },
                { icon: MessageSquare, title: 'Mensagens', value: '5,678', subtitle: 'Enviadas hoje', color: 'text-purple-600' },
                { icon: CheckCircle, title: 'Taxa de Sucesso', value: '98.5%', subtitle: 'Última campanha', color: 'text-emerald-600' }
            ];

            return React.createElement('div', { className: 'space-y-6' },
                React.createElement('div', { className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6' },
                    stats.map((stat, index) =>
                        React.createElement(Card, { key: index, className: 'hover:shadow-lg transition-shadow duration-200' },
                            React.createElement('div', { className: 'flex items-center gap-4' },
                                React.createElement('div', { className: `bg-gray-100 p-3 rounded-lg` },
                                    React.createElement(stat.icon, { className: `h-6 w-6 ${stat.color}` })
                                ),
                                React.createElement('div', {},
                                    React.createElement('p', { className: 'text-2xl font-bold text-gray-900' }, stat.value),
                                    React.createElement('p', { className: 'text-sm font-medium text-gray-600' }, stat.title),
                                    React.createElement('p', { className: 'text-xs text-gray-500' }, stat.subtitle)
                                )
                            )
                        )
                    )
                ),
                
                React.createElement(Card, {},
                    React.createElement('div', { className: 'text-center' },
                        React.createElement('h3', { className: 'text-lg font-semibold text-gray-800 mb-2' }, 'Bem-vindo ao Disparador Refatorado!'),
                        React.createElement('p', { className: 'text-gray-600 mb-4' }, 'Sistema completamente reformulado com arquitetura modular e componentes reutilizáveis.'),
                        React.createElement('div', { className: 'flex flex-col md:flex-row gap-4 justify-center' },
                            React.createElement(Button, {
                                onClick: () => addNotification('Funcionalidade em desenvolvimento!', 'info'),
                                icon: React.createElement(Send, { size: 18 })
                            }, 'Criar Nova Campanha'),
                            React.createElement(Button, {
                                variant: 'secondary',
                                onClick: () => addNotification('Contatos carregados com sucesso!', 'success'),
                                icon: React.createElement(Users, { size: 18 })
                            }, 'Gerenciar Contatos')
                        )
                    )
                )
            );
        };

        // Settings View
        const SettingsView = () => {
            const { addNotification } = useNotification();
            const [settings, setSettings] = useState({
                baseUrl: '',
                apiKey: '',
                minDelay: '2',
                maxDelay: '5'
            });

            const handleSave = (e) => {
                e.preventDefault();
                addNotification('Configurações salvas com sucesso!', 'success');
            };

            return React.createElement('div', { className: 'space-y-6' },
                React.createElement(Card, {},
                    React.createElement('div', { className: 'flex items-center gap-3 mb-6' },
                        React.createElement('div', { className: 'bg-blue-100 p-2 rounded-lg' },
                            React.createElement(SettingsIcon, { className: 'h-5 w-5 text-blue-600' })
                        ),
                        React.createElement('div', {},
                            React.createElement('h3', { className: 'font-semibold text-gray-800' }, 'Configurações da API'),
                            React.createElement('p', { className: 'text-sm text-gray-500' }, 'Defina a URL, chave e os delays de envio.')
                        )
                    ),
                    
                    React.createElement('form', { onSubmit: handleSave, className: 'space-y-6' },
                        React.createElement('div', {},
                            React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, 'URL Base da API'),
                            React.createElement('input', {
                                type: 'url',
                                value: settings.baseUrl,
                                onChange: (e) => setSettings({ ...settings, baseUrl: e.target.value }),
                                placeholder: 'https://sua-api.com',
                                className: 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                            })
                        ),
                        
                        React.createElement('div', {},
                            React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, 'API Key'),
                            React.createElement('input', {
                                type: 'password',
                                value: settings.apiKey,
                                onChange: (e) => setSettings({ ...settings, apiKey: e.target.value }),
                                placeholder: 'Sua chave de API',
                                className: 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                            })
                        ),
                        
                        React.createElement('div', { className: 'grid grid-cols-1 md:grid-cols-2 gap-4' },
                            React.createElement('div', {},
                                React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, 'Delay Mínimo (s)'),
                                React.createElement('input', {
                                    type: 'number',
                                    value: settings.minDelay,
                                    onChange: (e) => setSettings({ ...settings, minDelay: e.target.value }),
                                    className: 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                                })
                            ),
                            React.createElement('div', {},
                                React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, 'Delay Máximo (s)'),
                                React.createElement('input', {
                                    type: 'number',
                                    value: settings.maxDelay,
                                    onChange: (e) => setSettings({ ...settings, maxDelay: e.target.value }),
                                    className: 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                                })
                            )
                        ),
                        
                        React.createElement(Button, {
                            type: 'submit',
                            fullWidth: true,
                            icon: React.createElement(CheckCircle, { size: 18 })
                        }, 'Salvar Configurações')
                    )
                )
            );
        };

        // Main App Component
        const App = () => {
            const [currentView, setCurrentView] = useState('dashboard');

            const renderView = () => {
                switch (currentView) {
                    case 'dashboard':
                        return React.createElement(DashboardView);
                    case 'settings':
                        return React.createElement(SettingsView);
                    default:
                        return React.createElement('div', { className: 'text-center py-12' },
                            React.createElement(Card, {},
                                React.createElement('h3', { className: 'text-lg font-semibold text-gray-800 mb-2' }, `View: ${currentView}`),
                                React.createElement('p', { className: 'text-gray-600' }, 'Esta view está em desenvolvimento na versão refatorada.')
                            )
                        );
                }
            };

            return React.createElement(NotificationProvider, {},
                React.createElement('div', { className: 'bg-gray-50 min-h-screen w-full flex p-4 sm:p-6 lg:p-8 font-sans text-gray-900' },
                    React.createElement('div', { className: 'w-full max-w-7xl mx-auto' },
                        React.createElement(Header, { currentView, setView: setCurrentView }),
                        React.createElement('main', { className: 'animate-fade-in' },
                            renderView()
                        )
                    )
                )
            );
        };

        // Render the app
        ReactDOM.render(React.createElement(App), document.getElementById('root'));
    </script>
</body>
</html>
