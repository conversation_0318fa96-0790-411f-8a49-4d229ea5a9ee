// Tipos para o sistema de WhatsApp

export interface Message {
  id: string | number;
  text: string;
  mediaFile?: MediaFile | null;
}

export interface MediaFile {
  file?: File;
  type: string;
  name: string;
  base64?: string;
}

export interface Contact {
  id: string | number;
  name: string;
  phone: string;
  tags?: string[];
  [key: string]: any; // Para campos dinâmicos
}

export interface Group {
  id: string;
  name: string;
  participants?: number;
  description?: string;
}

export interface Template {
  id: string | number;
  name: string;
  messages: Message[];
}

export interface Instance {
  id: string;
  name: string;
  profileName?: string;
  connectionStatus: 'open' | 'close' | string;
}

export interface Settings {
  baseUrl: string;
  apiKey: string;
  minDelay: string;
  maxDelay: string;
  aiProvider: 'gemini' | 'openai';
  aiApiKey: string;
  systemPrompt: string;
}

export interface Stats {
  total: number;
  success: number;
  errors: number;
  lastRun: string | null;
}

export interface CampaignState {
  isSending: boolean;
  progress: number;
  success: number;
  errors: string[];
  current: number;
  total: number;
  message: string;
  config: CampaignConfig | null;
}

export interface CampaignConfig {
  targets: (Contact | Group)[];
  messages: Message[];
  instance: string;
  settings: Settings;
  sendMode: 'contacts' | 'groups';
  mentionEveryone: boolean;
}

export interface Notification {
  id: string | number;
  message: string;
  type: 'success' | 'error' | 'info';
}

export interface AiGenerationParams {
  objective: string;
  tone: string;
  details: string;
}

export type ViewType = 'dashboard' | 'main' | 'instances' | 'settings' | 'templates' | 'contacts' | 'groups';
