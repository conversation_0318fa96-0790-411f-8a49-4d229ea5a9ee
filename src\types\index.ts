// Enhanced types for WhatsApp Sender System with better type safety

// Base types
export type ViewType = 'dashboard' | 'main' | 'instances' | 'settings' | 'templates' | 'contacts' | 'groups';
export type NotificationType = 'success' | 'error' | 'info' | 'warning';
export type MediaType = 'image' | 'video' | 'audio' | 'application';
export type AIProvider = 'gemini' | 'openai';
export type ConnectionStatus = 'open' | 'close' | 'connecting' | 'disconnected';
export type CampaignStatus = 'idle' | 'running' | 'paused' | 'completed' | 'error';
export type SendMode = 'contacts' | 'groups';

// Enhanced interfaces
export interface MediaFile {
  file?: File;
  type: MediaType;
  name: string;
  size?: number;
  base64?: string;
  url?: string;
  thumbnail?: string;
}

export interface Message {
  id: string | number;
  text: string;
  mediaFile?: MediaFile | null;
  createdAt?: Date;
  updatedAt?: Date;
  order?: number;
}

export interface Contact {
  id: string | number;
  name: string;
  phone: string;
  email?: string;
  tags: string[];
  customFields: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;
  lastContactDate?: Date;
  isActive?: boolean;
  source?: string;
  notes?: string;
}

export interface Group {
  id: string;
  name: string;
  participants: number;
  description?: string;
  createdAt?: Date;
  updatedAt?: Date;
  isActive?: boolean;
  adminCount?: number;
  lastActivity?: Date;
}

export interface Template {
  id: string | number;
  name: string;
  description?: string;
  messages: Message[];
  category?: string;
  tags?: string[];
  createdAt?: Date;
  updatedAt?: Date;
  usageCount?: number;
  isActive?: boolean;
}

export interface Instance {
  id: string;
  name: string;
  profileName?: string;
  profilePicture?: string;
  connectionStatus: ConnectionStatus;
  lastSeen?: Date;
  phoneNumber?: string;
  qrCode?: string;
  webhookUrl?: string;
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface Settings {
  baseUrl: string;
  apiKey: string;
  minDelay: string;
  maxDelay: string;
  aiProvider: AIProvider;
  aiApiKey: string;
  systemPrompt: string;
  webhookUrl?: string;
  enableNotifications?: boolean;
  enableAnalytics?: boolean;
  maxRetries?: number;
  timeout?: number;
  theme?: 'light' | 'dark' | 'auto';
  language?: string;
}

export interface Stats {
  total: number;
  success: number;
  errors: number;
  lastRun: string | null;
  averageDeliveryTime?: number;
  totalCampaigns?: number;
  totalContacts?: number;
  totalGroups?: number;
  monthlyStats?: {
    sent: number;
    delivered: number;
    failed: number;
  };
}

export interface CampaignState {
  isSending: boolean;
  status?: CampaignStatus;
  progress: number;
  success: number;
  errors: string[];
  current: number;
  total: number;
  message: string;
  config: CampaignConfig | null;
  startTime?: Date;
  endTime?: Date;
  estimatedTimeRemaining?: number;
  currentTarget?: Contact | Group;
}

export interface CampaignConfig {
  id?: string;
  name?: string;
  targets: (Contact | Group)[];
  messages: Message[];
  instance: string;
  settings: Settings;
  sendMode: SendMode;
  mentionEveryone: boolean;
  scheduledTime?: Date;
  timezone?: string;
  filters?: CampaignFilters;
  options?: CampaignOptions;
}

export interface CampaignFilters {
  tags?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  customFields?: Record<string, any>;
}

export interface CampaignOptions {
  respectOptOut?: boolean;
  trackDelivery?: boolean;
  enableRetries?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  enableAnalytics?: boolean;
}

export interface Notification {
  id: string | number;
  message: string;
  type: NotificationType;
  title?: string;
  duration?: number;
  persistent?: boolean;
  actions?: NotificationAction[];
  createdAt?: Date;
}

export interface NotificationAction {
  label: string;
  action: () => void;
  variant?: 'primary' | 'secondary' | 'destructive';
}

export interface AiGenerationParams {
  objective: string;
  tone: string;
  details: string;
}

export type ViewType = 'dashboard' | 'main' | 'instances' | 'settings' | 'templates' | 'contacts' | 'groups';
