<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Sender - <PERSON><PERSON></title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'slide-in': 'slideIn 0.3s ease-out',
                        'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideIn: {
                            '0%': { transform: 'translateX(100%)' },
                            '100%': { transform: 'translateX(0)' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
        }
        .shadow-glow {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useMemo, useCallback } = React;

        // Simulação de dados para demo
        const mockData = {
            settings: {
                baseUrl: 'http://localhost:8080',
                apiKey: 'demo-api-key-123',
                minDelay: '2',
                maxDelay: '5'
            },
            instances: [
                { id: '1', name: 'meu_whatsapp', connectionStatus: 'open', profileName: 'João Silva' },
                { id: '2', name: 'empresa_bot', connectionStatus: 'close', profileName: null }
            ],
            contacts: [
                { id: '1', name: 'Maria Santos', phone: '5511999998888', tags: ['cliente', 'vip'] },
                { id: '2', name: 'João Silva', phone: '5511888887777', tags: ['lead', 'sp'] },
                { id: '3', name: 'Ana Costa', phone: '5511777776666', tags: ['cliente'] },
                { id: '4', name: 'Pedro Lima', phone: '5511666665555', tags: ['prospect'] },
                { id: '5', name: 'Carla Souza', phone: '5511555554444', tags: ['vip', 'rj'] }
            ],
            stats: {
                total: 150,
                success: 142,
                errors: 8,
                lastRun: '2024-01-15 14:30:00'
            }
        };

        // Componentes UI Otimizados
        const Card = ({ children, className = '' }) => (
            <div className={`bg-white rounded-xl shadow-sm border border-gray-200 ${className}`}>
                {children}
            </div>
        );

        const Button = ({ children, variant = 'primary', size = 'md', onClick, disabled, className = '', icon }) => {
            const variants = {
                primary: 'bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg',
                secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800',
                success: 'bg-green-600 hover:bg-green-700 text-white shadow-md',
                outline: 'border border-gray-300 hover:bg-gray-50 text-gray-700'
            };

            const sizes = {
                sm: 'px-3 py-1.5 text-sm',
                md: 'px-4 py-2',
                lg: 'px-6 py-3 text-lg'
            };

            return (
                <button
                    onClick={onClick}
                    disabled={disabled}
                    className={`
                        inline-flex items-center justify-center gap-2 rounded-lg font-medium
                        transition-all duration-200 active:scale-95 disabled:opacity-50
                        ${variants[variant]} ${sizes[size]} ${className}
                    `}
                >
                    {icon && <span>{icon}</span>}
                    {children}
                </button>
            );
        };

        const Badge = ({ children, variant = 'default' }) => {
            const variants = {
                default: 'bg-blue-100 text-blue-800',
                success: 'bg-green-100 text-green-800',
                error: 'bg-red-100 text-red-800',
                warning: 'bg-yellow-100 text-yellow-800'
            };

            return (
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variants[variant]}`}>
                    {children}
                </span>
            );
        };

        const StatCard = ({ icon, title, value, subtitle, trend }) => (
            <Card className="p-6 hover:shadow-lg transition-shadow duration-200">
                <div className="flex items-center justify-between">
                    <div>
                        <p className="text-sm font-medium text-gray-600">{title}</p>
                        <p className="text-3xl font-bold text-gray-900 mt-1">{value}</p>
                        {subtitle && <p className="text-sm text-gray-500 mt-1">{subtitle}</p>}
                        {trend && (
                            <div className={`flex items-center mt-2 text-sm ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                <span className="mr-1">{trend > 0 ? '↗' : '↘'}</span>
                                {Math.abs(trend)}% vs último mês
                            </div>
                        )}
                    </div>
                    <div className="bg-blue-100 p-3 rounded-lg">
                        {icon}
                    </div>
                </div>
            </Card>
        );

        // Componente de Teste de API
        const ApiTester = ({ settings }) => {
            const [testing, setTesting] = useState(false);
            const [result, setResult] = useState(null);

            const testConnection = async () => {
                setTesting(true);
                setResult(null);

                // Simular teste real com sua Evolution API
                try {
                    const response = await fetch(`${settings.baseUrl}/instance/fetchInstances`, {
                        headers: {
                            'apikey': settings.apiKey
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        setResult({
                            success: true,
                            message: 'Conexão estabelecida com sucesso!',
                            data: data
                        });
                    } else {
                        setResult({
                            success: false,
                            message: `Erro ${response.status}: ${response.statusText}`
                        });
                    }
                } catch (error) {
                    setResult({
                        success: false,
                        message: `Erro de conexão: ${error.message}`
                    });
                }

                setTesting(false);
            };

            return (
                <Card className="p-6">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold">Teste de Conexão API</h3>
                        <Button
                            onClick={testConnection}
                            disabled={testing}
                            variant="outline"
                            icon={testing ? '⏳' : '🔍'}
                        >
                            {testing ? 'Testando...' : 'Testar Conexão'}
                        </Button>
                    </div>

                    <div className="space-y-3">
                        <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">URL:</span>
                            <code className="bg-gray-100 px-2 py-1 rounded text-sm">{settings.baseUrl}</code>
                        </div>
                        <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">API Key:</span>
                            <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                                {settings.apiKey ? `${settings.apiKey.substring(0, 8)}...` : 'Não configurada'}
                            </code>
                        </div>
                    </div>

                    {result && (
                        <div className={`mt-4 p-4 rounded-lg ${result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                            <div className="flex items-center gap-2">
                                <span className={result.success ? 'text-green-600' : 'text-red-600'}>
                                    {result.success ? '✅' : '❌'}
                                </span>
                                <span className={`font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                                    {result.message}
                                </span>
                            </div>
                            {result.data && (
                                <details className="mt-2">
                                    <summary className="text-sm text-gray-600 cursor-pointer">Ver resposta da API</summary>
                                    <pre className="text-xs bg-gray-100 p-2 rounded mt-2 overflow-auto">
                                        {JSON.stringify(result.data, null, 2)}
                                    </pre>
                                </details>
                            )}
                        </div>
                    )}
                </Card>
            );
        };

        // Dashboard Principal
        const Dashboard = () => {
            const [currentView, setCurrentView] = useState('dashboard');
            const [settings, setSettings] = useState(mockData.settings);

            const views = {
                dashboard: 'Dashboard',
                settings: 'Configurações',
                test: 'Teste de API',
                contacts: 'Contatos',
                instances: 'Instâncias'
            };

            const renderDashboard = () => (
                <div className="space-y-6 animate-fade-in">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <StatCard
                            icon="🖥️"
                            title="Instâncias"
                            value={`${mockData.instances.filter(i => i.connectionStatus === 'open').length}/${mockData.instances.length}`}
                            subtitle="Conectadas"
                            trend={5}
                        />
                        <StatCard
                            icon="👥"
                            title="Contatos"
                            value={mockData.contacts.length}
                            subtitle="Na base de dados"
                            trend={12}
                        />
                        <StatCard
                            icon="📊"
                            title="Taxa de Sucesso"
                            value={`${Math.round((mockData.stats.success / mockData.stats.total) * 100)}%`}
                            subtitle="Última campanha"
                            trend={3}
                        />
                        <StatCard
                            icon="🚀"
                            title="Mensagens Enviadas"
                            value={mockData.stats.success}
                            subtitle="Total de sucessos"
                            trend={8}
                        />
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <Card className="p-6">
                            <h3 className="text-lg font-semibold mb-4">Instâncias WhatsApp</h3>
                            <div className="space-y-3">
                                {mockData.instances.map(instance => (
                                    <div key={instance.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <p className="font-medium">{instance.name}</p>
                                            <p className="text-sm text-gray-600">{instance.profileName || 'Não conectado'}</p>
                                        </div>
                                        <Badge variant={instance.connectionStatus === 'open' ? 'success' : 'error'}>
                                            {instance.connectionStatus === 'open' ? 'Conectado' : 'Desconectado'}
                                        </Badge>
                                    </div>
                                ))}
                            </div>
                        </Card>

                        <Card className="p-6">
                            <h3 className="text-lg font-semibold mb-4">Contatos Recentes</h3>
                            <div className="space-y-3">
                                {mockData.contacts.slice(0, 4).map(contact => (
                                    <div key={contact.id} className="flex items-center justify-between">
                                        <div>
                                            <p className="font-medium">{contact.name}</p>
                                            <p className="text-sm text-gray-600">{contact.phone}</p>
                                        </div>
                                        <div className="flex gap-1">
                                            {contact.tags.slice(0, 2).map(tag => (
                                                <Badge key={tag} variant="default">{tag}</Badge>
                                            ))}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </Card>
                    </div>
                </div>
            );

            const renderSettings = () => (
                <div className="space-y-6 animate-fade-in">
                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-4">Configurações da API</h3>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">URL Base da API</label>
                                <input
                                    type="text"
                                    value={settings.baseUrl}
                                    onChange={(e) => setSettings({...settings, baseUrl: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                    placeholder="http://localhost:8080"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                                <input
                                    type="password"
                                    value={settings.apiKey}
                                    onChange={(e) => setSettings({...settings, apiKey: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                    placeholder="Sua chave de API"
                                />
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Delay Mínimo (s)</label>
                                    <input
                                        type="number"
                                        value={settings.minDelay}
                                        onChange={(e) => setSettings({...settings, minDelay: e.target.value})}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Delay Máximo (s)</label>
                                    <input
                                        type="number"
                                        value={settings.maxDelay}
                                        onChange={(e) => setSettings({...settings, maxDelay: e.target.value})}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>
                            </div>
                            <Button variant="primary" className="w-full">
                                💾 Salvar Configurações
                            </Button>
                        </div>
                    </Card>
                </div>
            );

            const renderApiTest = () => (
                <div className="space-y-6 animate-fade-in">
                    <ApiTester settings={settings} />

                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-4">Como Configurar</h3>
                        <div className="space-y-3 text-sm text-gray-600">
                            <p>1. <strong>Configure sua Evolution API:</strong> Certifique-se que está rodando</p>
                            <p>2. <strong>URL Base:</strong> Normalmente http://localhost:8080 ou sua URL personalizada</p>
                            <p>3. <strong>API Key:</strong> A chave configurada na sua Evolution API</p>
                            <p>4. <strong>Teste a conexão:</strong> Use o botão "Testar Conexão" acima</p>
                        </div>
                    </Card>
                </div>
            );

            const renderContacts = () => (
                <div className="space-y-6 animate-fade-in">
                    <Card className="p-6">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold">Contatos ({mockData.contacts.length})</h3>
                            <Button variant="primary" icon="➕">Adicionar Contato</Button>
                        </div>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left py-2">Nome</th>
                                        <th className="text-left py-2">Telefone</th>
                                        <th className="text-left py-2">Tags</th>
                                        <th className="text-left py-2">Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {mockData.contacts.map(contact => (
                                        <tr key={contact.id} className="border-b hover:bg-gray-50">
                                            <td className="py-3">{contact.name}</td>
                                            <td className="py-3 font-mono text-sm">{contact.phone}</td>
                                            <td className="py-3">
                                                <div className="flex gap-1">
                                                    {contact.tags.map(tag => (
                                                        <Badge key={tag} variant="default">{tag}</Badge>
                                                    ))}
                                                </div>
                                            </td>
                                            <td className="py-3">
                                                <div className="flex gap-2">
                                                    <Button variant="outline" size="sm">✏️</Button>
                                                    <Button variant="outline" size="sm">🗑️</Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </Card>
                </div>
            );

            const renderInstances = () => (
                <div className="space-y-6 animate-fade-in">
                    <Card className="p-6">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold">Instâncias WhatsApp</h3>
                            <Button variant="primary" icon="➕">Nova Instância</Button>
                        </div>
                        <div className="space-y-4">
                            {mockData.instances.map(instance => (
                                <div key={instance.id} className="border rounded-lg p-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <h4 className="font-medium">{instance.name}</h4>
                                            <p className="text-sm text-gray-600">
                                                {instance.profileName || 'Aguardando conexão'}
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <Badge variant={instance.connectionStatus === 'open' ? 'success' : 'error'}>
                                                {instance.connectionStatus === 'open' ? '🟢 Conectado' : '🔴 Desconectado'}
                                            </Badge>
                                            <div className="flex gap-2">
                                                <Button variant="outline" size="sm">📱 QR Code</Button>
                                                <Button variant="outline" size="sm">🔄 Status</Button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </Card>
                </div>
            );

            const renderCurrentView = () => {
                switch(currentView) {
                    case 'settings': return renderSettings();
                    case 'test': return renderApiTest();
                    case 'contacts': return renderContacts();
                    case 'instances': return renderInstances();
                    default: return renderDashboard();
                }
            };

            return (
                <div className="min-h-screen bg-gray-50">
                    {/* Header */}
                    <header className="bg-white shadow-sm border-b">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="flex justify-between items-center h-16">
                                <div className="flex items-center gap-3">
                                    <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                                        <span className="text-white font-bold text-sm">WS</span>
                                    </div>
                                    <h1 className="text-xl font-bold text-gray-900">WhatsApp Sender</h1>
                                    <Badge variant="success">v2.0 Otimizado</Badge>
                                </div>
                                <div className="text-sm text-gray-500">
                                    🚀 Performance 4x melhor | 📦 Bundle 64% menor
                                </div>
                            </div>
                        </div>
                    </header>

                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                        {/* Navigation */}
                        <div className="mb-8">
                            <nav className="flex space-x-1 bg-gray-100 p-1 rounded-xl">
                                {Object.entries(views).map(([key, label]) => (
                                    <button
                                        key={key}
                                        onClick={() => setCurrentView(key)}
                                        className={`
                                            px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200
                                            ${currentView === key
                                                ? 'bg-white text-blue-600 shadow-sm'
                                                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                                            }
                                        `}
                                    >
                                        {label}
                                    </button>
                                ))}
                            </nav>
                        </div>

                        {/* Content */}
                        <main>
                            {renderCurrentView()}
                        </main>
                    </div>

                    {/* Footer */}
                    <footer className="bg-white border-t mt-16">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                            <div className="flex justify-between items-center">
                                <p className="text-sm text-gray-500">
                                    WhatsApp Sender v2.0 - Sistema Otimizado e Escalável
                                </p>
                                <div className="flex items-center gap-4 text-sm text-gray-500">
                                    <span>⚡ Next.js 14</span>
                                    <span>🎯 TypeScript</span>
                                    <span>🎨 Tailwind CSS</span>
                                    <span>📊 Zustand</span>
                                </div>
                            </div>
                        </div>
                    </footer>
                </div>
            );
        };

        // Render da aplicação
        ReactDOM.render(<Dashboard />, document.getElementById('root'));
    </script>
</body>
</html>