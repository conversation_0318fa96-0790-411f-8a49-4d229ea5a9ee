# 🚀 Guia de Configuração - WhatsApp Sender

## 📋 Passo a Passo Completo

### 1. 🔧 Configurar API WhatsApp

#### Opção A: Evolution API (Recomendada)

**Instalação com Docker:**
```bash
# Criar docker-compose.yml
version: '3.8'
services:
  evolution-api:
    image: atendai/evolution-api:latest
    ports:
      - "8080:8080"
    environment:
      - SERVER_URL=http://localhost:8080
      - AUTHENTICATION_API_KEY=minha-chave-super-secreta
      - AUTHENTICATION_EXPOSE_IN_FETCH_INSTANCES=true
    volumes:
      - evolution_instances:/evolution/instances
      - evolution_store:/evolution/store

volumes:
  evolution_instances:
  evolution_store:
```

**Iniciar:**
```bash
docker-compose up -d
```

**Testar:**
```bash
curl -X GET "http://localhost:8080/instance/fetchInstances" \
  -H "apikey: minha-chave-super-secreta"
```

#### Opção B: Baileys API

**Instalação:**
```bash
git clone https://github.com/WhiskeySockets/Baileys
cd Baileys
npm install
npm start
```

### 2. 📱 Configurar o Sistema

#### Clonar e Instalar
```bash
# Clone o projeto
git clone https://github.com/whatsapp-sender-optimized.git
cd whatsapp-sender-optimized

# Instalar dependências
npm install
```

#### Configurar Variáveis de Ambiente
```bash
# Copiar arquivo de exemplo
cp .env.example .env.local

# Editar configurações
nano .env.local
```

**Configuração .env.local:**
```bash
# API Configuration
NEXT_PUBLIC_API_BASE_URL="http://localhost:8080"
NEXT_PUBLIC_API_KEY="minha-chave-super-secreta"

# Performance
NEXT_PUBLIC_ENABLE_WEB_WORKERS=true
NEXT_PUBLIC_ENABLE_VIRTUAL_SCROLLING=true
NEXT_PUBLIC_CACHE_TTL=300000

# Analytics (opcional)
NEXT_PUBLIC_ANALYTICS_ENABLED=true
NEXT_PUBLIC_ERROR_TRACKING_ENABLED=true
```

### 3. 🚀 Iniciar o Sistema

```bash
# Desenvolvimento
npm run dev

# Produção
npm run build
npm start
```

**Acessar:** http://localhost:3000

### 4. 📲 Configurar Instância WhatsApp

#### No Sistema:
1. Acesse **Configurações**
2. Configure:
   - **URL Base da API:** `http://localhost:8080`
   - **API Key:** `minha-chave-super-secreta`
3. Salve as configurações

#### Criar Instância:
1. Vá para **Instâncias**
2. Digite um nome: `meu_whatsapp`
3. Clique **Criar**
4. Escaneie o QR Code com seu WhatsApp
5. Aguarde conexão

### 5. 📞 Testar Envio

#### Adicionar Contatos:
1. Vá para **Contatos**
2. Clique **Adicionar Manual**
3. Preencha:
   - **Nome:** João Silva
   - **Telefone:** 5511999998888
   - **Tags:** teste
4. Salve

#### Enviar Mensagem:
1. Vá para **Disparador**
2. Selecione instância: `meu_whatsapp`
3. Digite mensagem: `Olá {{nome}}! Esta é uma mensagem de teste.`
4. Clique **Iniciar Disparos**

## 🔍 Troubleshooting

### ❌ Erro: "API Key não configurada"

**Solução:**
```bash
# Verificar .env.local
cat .env.local | grep API_KEY

# Deve mostrar:
NEXT_PUBLIC_API_KEY="sua-chave-aqui"
```

### ❌ Erro: "Erro de conexão"

**Verificar API:**
```bash
# Testar se API está rodando
curl http://localhost:8080/instance/fetchInstances \
  -H "apikey: sua-chave"

# Deve retornar JSON com instâncias
```

**Verificar Docker:**
```bash
# Ver logs da API
docker-compose logs evolution-api

# Reiniciar se necessário
docker-compose restart
```

### ❌ Erro: "Instância não conectada"

**Soluções:**
1. Gerar novo QR Code
2. Verificar se WhatsApp está ativo no celular
3. Tentar com outro número

### ❌ Erro: "Falha ao enviar mensagem"

**Verificar:**
1. Número está correto (com DDI)
2. Instância está conectada
3. API está funcionando
4. Rate limit não foi atingido

## 🛠️ Configurações Avançadas

### Rate Limiting
```bash
# .env.local
NEXT_PUBLIC_MIN_DELAY=2000  # 2 segundos
NEXT_PUBLIC_MAX_DELAY=5000  # 5 segundos
```

### Webhook (Opcional)
```bash
# .env.local
WEBHOOK_URL="https://seu-site.com/webhook"
WEBHOOK_SECRET="sua-chave-webhook"
```

### Performance
```bash
# .env.local
NEXT_PUBLIC_CACHE_TTL=300000        # 5 minutos
NEXT_PUBLIC_ENABLE_WEB_WORKERS=true
NEXT_PUBLIC_ENABLE_VIRTUAL_SCROLLING=true
```

## 📊 Monitoramento

### Logs da API
```bash
# Evolution API
docker-compose logs -f evolution-api

# Sistema
npm run dev  # Logs no terminal
```

### Métricas
- Acesse **Dashboard** para ver estatísticas
- **Performance** em tempo real
- **Erros** e **sucessos** de campanhas

### Health Check
```bash
# Verificar saúde da API
curl http://localhost:8080/health

# Verificar sistema
curl http://localhost:3000/api/health
```

## 🔒 Segurança

### Produção
```bash
# Usar HTTPS
NEXT_PUBLIC_API_BASE_URL="https://sua-api.com"

# API Key forte
NEXT_PUBLIC_API_KEY="chave-complexa-e-segura-123"

# Headers de segurança
NEXT_PUBLIC_CSP_ENABLED=true
```

### Backup
```bash
# Backup dados Evolution API
docker-compose exec evolution-api tar -czf /tmp/backup.tar.gz /evolution

# Backup configurações sistema
cp .env.local .env.backup
```

## 📱 Uso Mobile

### PWA
1. Acesse pelo celular
2. Chrome: **Adicionar à tela inicial**
3. Safari: **Compartilhar** > **Adicionar à tela inicial**

### Responsividade
- Interface otimizada para mobile
- Touch gestures
- Navegação simplificada

## 🎯 Próximos Passos

### Funcionalidades Extras
1. **Templates avançados**
2. **Agendamento** de campanhas
3. **Relatórios** detalhados
4. **Integração** com CRM
5. **API própria** para integrações

### Escalabilidade
1. **Load balancer** para múltiplas APIs
2. **Database** para dados persistentes
3. **Queue system** para campanhas grandes
4. **Microservices** architecture

## 📞 Suporte

### Documentação
- [Evolution API](https://doc.evolutionapi.com)
- [Next.js](https://nextjs.org/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)

### Comunidade
- GitHub Issues
- Discord/Telegram groups
- Stack Overflow

### Contato
- Email: <EMAIL>
- WhatsApp: +55 11 99999-8888

---

**✅ Sistema configurado e pronto para uso!**

**🚀 Comece enviando suas primeiras mensagens em massa!**
