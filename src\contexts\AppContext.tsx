import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { 
  Settings, 
  Contact, 
  Group, 
  Template, 
  Instance, 
  Stats, 
  CampaignState, 
  ViewType 
} from '../types';
import { 
  settingsStorage, 
  contactsStorage, 
  groupsStorage, 
  templatesStorage, 
  statsStorage, 
  tagsStorage,
  campaignProgressStorage 
} from '../utils/storage';

interface AppState {
  // UI State
  currentView: ViewType;
  loading: boolean;
  
  // Data State
  settings: Settings;
  contacts: Contact[];
  groups: Group[];
  templates: Template[];
  instances: Instance[];
  stats: Stats;
  allTags: string[];
  
  // Campaign State
  campaignState: CampaignState;
}

type AppAction = 
  | { type: 'SET_VIEW'; payload: ViewType }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_SETTINGS'; payload: Settings }
  | { type: 'SET_CONTACTS'; payload: Contact[] }
  | { type: 'SET_GROUPS'; payload: Group[] }
  | { type: 'SET_TEMPLATES'; payload: Template[] }
  | { type: 'SET_INSTANCES'; payload: Instance[] }
  | { type: 'SET_STATS'; payload: Stats }
  | { type: 'SET_ALL_TAGS'; payload: string[] }
  | { type: 'SET_CAMPAIGN_STATE'; payload: CampaignState }
  | { type: 'UPDATE_CAMPAIGN_PROGRESS'; payload: Partial<CampaignState> };

const initialState: AppState = {
  currentView: 'dashboard',
  loading: false,
  settings: settingsStorage.load(),
  contacts: contactsStorage.load(),
  groups: groupsStorage.load(),
  templates: templatesStorage.load(),
  instances: [],
  stats: statsStorage.load(),
  allTags: tagsStorage.load(),
  campaignState: {
    isSending: false,
    progress: 0,
    success: 0,
    errors: [],
    current: 0,
    total: 0,
    message: '',
    config: null,
  },
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_VIEW':
      return { ...state, currentView: action.payload };
    
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_SETTINGS':
      settingsStorage.save(action.payload);
      return { ...state, settings: action.payload };
    
    case 'SET_CONTACTS':
      contactsStorage.save(action.payload);
      return { ...state, contacts: action.payload };
    
    case 'SET_GROUPS':
      groupsStorage.save(action.payload);
      return { ...state, groups: action.payload };
    
    case 'SET_TEMPLATES':
      templatesStorage.save(action.payload);
      return { ...state, templates: action.payload };
    
    case 'SET_INSTANCES':
      return { ...state, instances: action.payload };
    
    case 'SET_STATS':
      statsStorage.save(action.payload);
      return { ...state, stats: action.payload };
    
    case 'SET_ALL_TAGS':
      tagsStorage.save(action.payload);
      return { ...state, allTags: action.payload };
    
    case 'SET_CAMPAIGN_STATE':
      return { ...state, campaignState: action.payload };
    
    case 'UPDATE_CAMPAIGN_PROGRESS':
      return { 
        ...state, 
        campaignState: { ...state.campaignState, ...action.payload } 
      };
    
    default:
      return state;
  }
};

interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  
  // Helper functions
  setView: (view: ViewType) => void;
  setLoading: (loading: boolean) => void;
  updateSettings: (settings: Settings) => void;
  updateContacts: (contacts: Contact[]) => void;
  updateGroups: (groups: Group[]) => void;
  updateTemplates: (templates: Template[]) => void;
  updateInstances: (instances: Instance[]) => void;
  updateStats: (stats: Stats) => void;
  updateAllTags: (tags: string[]) => void;
  updateCampaignState: (campaignState: CampaignState) => void;
  updateCampaignProgress: (progress: Partial<CampaignState>) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Helper functions
  const setView = useCallback((view: ViewType) => {
    dispatch({ type: 'SET_VIEW', payload: view });
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  }, []);

  const updateSettings = useCallback((settings: Settings) => {
    dispatch({ type: 'SET_SETTINGS', payload: settings });
  }, []);

  const updateContacts = useCallback((contacts: Contact[]) => {
    dispatch({ type: 'SET_CONTACTS', payload: contacts });
    
    // Update tags automatically
    const newTagSet = new Set(state.allTags);
    contacts.forEach(contact => {
      if (contact.tags && Array.isArray(contact.tags)) {
        contact.tags.forEach(tag => newTagSet.add(tag));
      }
    });
    const newTags = Array.from(newTagSet);
    dispatch({ type: 'SET_ALL_TAGS', payload: newTags });
  }, [state.allTags]);

  const updateGroups = useCallback((groups: Group[]) => {
    dispatch({ type: 'SET_GROUPS', payload: groups });
  }, []);

  const updateTemplates = useCallback((templates: Template[]) => {
    dispatch({ type: 'SET_TEMPLATES', payload: templates });
  }, []);

  const updateInstances = useCallback((instances: Instance[]) => {
    dispatch({ type: 'SET_INSTANCES', payload: instances });
  }, []);

  const updateStats = useCallback((stats: Stats) => {
    dispatch({ type: 'SET_STATS', payload: stats });
  }, []);

  const updateAllTags = useCallback((tags: string[]) => {
    dispatch({ type: 'SET_ALL_TAGS', payload: tags });
  }, []);

  const updateCampaignState = useCallback((campaignState: CampaignState) => {
    dispatch({ type: 'SET_CAMPAIGN_STATE', payload: campaignState });
  }, []);

  const updateCampaignProgress = useCallback((progress: Partial<CampaignState>) => {
    dispatch({ type: 'UPDATE_CAMPAIGN_PROGRESS', payload: progress });
  }, []);

  // Load saved campaign progress on mount
  useEffect(() => {
    const savedProgress = campaignProgressStorage.load();
    if (savedProgress && savedProgress.config) {
      const total = savedProgress.config.targets.length;
      const remaining = savedProgress.remainingTargets.length;
      const processed = total - remaining;
      
      updateCampaignState({
        isSending: false,
        progress: (processed / total) * 100,
        success: savedProgress.success,
        errors: savedProgress.errors,
        current: processed,
        total: total,
        config: savedProgress.config,
        message: 'Campanha pausada.',
      });
    }
  }, [updateCampaignState]);

  const value: AppContextType = {
    state,
    dispatch,
    setView,
    setLoading,
    updateSettings,
    updateContacts,
    updateGroups,
    updateTemplates,
    updateInstances,
    updateStats,
    updateAllTags,
    updateCampaignState,
    updateCampaignProgress,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

export const useApp = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};
