'use client';

import React, { Suspense, lazy } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ErrorBoundary } from 'react-error-boundary';
import { Toaster } from 'sonner';
import { useAppStore } from '@/stores/appStore';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { OptimizedButton } from '@/components/ui/OptimizedButton';
import { cacheManager } from '@/utils/cache';
import { 
  LayoutDashboard, 
  Send, 
  Users, 
  Server, 
  Bookmark, 
  Settings as SettingsIcon,
  RefreshCw,
  AlertCircle
} from 'lucide-react';

// Lazy load views for better performance
const OptimizedDashboard = lazy(() => import('@/components/views/OptimizedDashboard').then(m => ({ default: m.OptimizedDashboard })));
const OptimizedContacts = lazy(() => import('@/components/views/OptimizedContacts').then(m => ({ default: m.OptimizedContacts })));
const CampaignView = lazy(() => import('@/components/views/CampaignView'));
const InstancesView = lazy(() => import('@/components/views/InstancesView'));
const TemplatesView = lazy(() => import('@/components/views/TemplatesView'));
const SettingsView = lazy(() => import('@/components/views/SettingsView'));
const GroupsView = lazy(() => import('@/components/views/GroupsView'));

// Create a stable query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
    },
  },
});

// Navigation component
const Navigation = React.memo(() => {
  const currentView = useAppStore(state => state.currentView);
  const setView = useAppStore(state => state.setView);
  const sidebarCollapsed = useAppStore(state => state.sidebarCollapsed);
  const toggleSidebar = useAppStore(state => state.toggleSidebar);

  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { id: 'main', label: 'Disparador', icon: Send },
    { id: 'contacts', label: 'Contatos', icon: Users },
    { id: 'groups', label: 'Grupos', icon: Users },
    { id: 'instances', label: 'Instâncias', icon: Server },
    { id: 'templates', label: 'Templates', icon: Bookmark },
  ];

  return (
    <nav className={`bg-white border-r border-gray-200 transition-all duration-300 ${sidebarCollapsed ? 'w-16' : 'w-64'}`}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-8">
          {!sidebarCollapsed && (
            <h1 className="text-xl font-bold text-gray-900">WhatsApp Sender</h1>
          )}
          <OptimizedButton
            onClick={toggleSidebar}
            variant="ghost"
            size="icon-sm"
            tooltip={sidebarCollapsed ? "Expandir" : "Recolher"}
          >
            <LayoutDashboard size={18} />
          </OptimizedButton>
        </div>

        <div className="space-y-2">
          {navItems.map(item => (
            <OptimizedButton
              key={item.id}
              onClick={() => setView(item.id as any)}
              variant={currentView === item.id ? 'default' : 'ghost'}
              fullWidth
              leftIcon={<item.icon size={18} />}
              className={`justify-start ${sidebarCollapsed ? 'px-2' : 'px-4'}`}
              tooltip={sidebarCollapsed ? item.label : undefined}
            >
              {!sidebarCollapsed && item.label}
            </OptimizedButton>
          ))}
        </div>

        <div className="mt-8 pt-8 border-t border-gray-200">
          <OptimizedButton
            onClick={() => setView('settings')}
            variant={currentView === 'settings' ? 'default' : 'ghost'}
            fullWidth
            leftIcon={<SettingsIcon size={18} />}
            className={`justify-start ${sidebarCollapsed ? 'px-2' : 'px-4'}`}
            tooltip={sidebarCollapsed ? "Configurações" : undefined}
          >
            {!sidebarCollapsed && 'Configurações'}
          </OptimizedButton>
        </div>
      </div>
    </nav>
  );
});

Navigation.displayName = 'Navigation';

// Main content component
const MainContent = React.memo(() => {
  const currentView = useAppStore(state => state.currentView);
  const setView = useAppStore(state => state.setView);

  const renderView = () => {
    switch (currentView) {
      case 'dashboard':
        return <OptimizedDashboard onSwitchView={setView} />;
      case 'main':
        return <CampaignView onSwitchView={setView} />;
      case 'contacts':
        return <OptimizedContacts onSwitchView={setView} />;
      case 'groups':
        return <GroupsView onSwitchView={setView} />;
      case 'instances':
        return <InstancesView onSwitchView={setView} />;
      case 'templates':
        return <TemplatesView onSwitchView={setView} />;
      case 'settings':
        return <SettingsView onSwitchView={setView} />;
      default:
        return <OptimizedDashboard onSwitchView={setView} />;
    }
  };

  return (
    <main className="flex-1 overflow-auto">
      <div className="p-6">
        <Suspense 
          fallback={
            <div className="flex items-center justify-center h-64">
              <LoadingSpinner size="lg" />
            </div>
          }
        >
          {renderView()}
        </Suspense>
      </div>
    </main>
  );
});

MainContent.displayName = 'MainContent';

// Error fallback component
const ErrorFallback = ({ error, resetErrorBoundary }: any) => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center gap-3 text-red-600 mb-4">
        <AlertCircle className="h-6 w-6" />
        <h2 className="text-lg font-semibold">Algo deu errado</h2>
      </div>
      
      <p className="text-gray-600 mb-4">
        Ocorreu um erro inesperado. Você pode tentar recarregar a página ou entrar em contato com o suporte.
      </p>
      
      <details className="mb-4">
        <summary className="text-sm text-gray-500 cursor-pointer">Detalhes do erro</summary>
        <pre className="text-xs text-gray-400 mt-2 p-2 bg-gray-100 rounded overflow-auto">
          {error.message}
        </pre>
      </details>
      
      <div className="flex gap-3">
        <OptimizedButton
          onClick={resetErrorBoundary}
          variant="default"
          leftIcon={<RefreshCw size={16} />}
        >
          Tentar Novamente
        </OptimizedButton>
        
        <OptimizedButton
          onClick={() => window.location.reload()}
          variant="outline"
        >
          Recarregar Página
        </OptimizedButton>
      </div>
    </div>
  </div>
);

// Main App component
export default function App() {
  // Initialize cache on app start
  React.useEffect(() => {
    cacheManager.init();
  }, []);

  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <QueryClientProvider client={queryClient}>
        <div className="min-h-screen bg-gray-50 flex">
          <Navigation />
          <MainContent />
        </div>
        
        {/* Toast notifications */}
        <Toaster 
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '0.5rem',
            },
          }}
        />
        
        {/* React Query DevTools (only in development) */}
        {process.env.NODE_ENV === 'development' && (
          <ReactQueryDevtools initialIsOpen={false} />
        )}
      </QueryClientProvider>
    </ErrorBoundary>
  );
}
