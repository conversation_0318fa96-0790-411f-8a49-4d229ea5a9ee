import React, { memo, forwardRef, useMemo } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/utils/cn';
import { LoadingSpinner } from './LoadingSpinner';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background active:scale-95 select-none',
  {
    variants: {
      variant: {
        default: 'bg-blue-600 text-white hover:bg-blue-700 shadow-sm hover:shadow-md',
        destructive: 'bg-red-600 text-white hover:bg-red-700 shadow-sm hover:shadow-md',
        outline: 'border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 shadow-sm hover:shadow-md',
        secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 shadow-sm',
        ghost: 'hover:bg-gray-100 text-gray-700 hover:text-gray-900',
        link: 'underline-offset-4 hover:underline text-blue-600 hover:text-blue-700',
        success: 'bg-green-600 text-white hover:bg-green-700 shadow-sm hover:shadow-md',
        warning: 'bg-yellow-600 text-white hover:bg-yellow-700 shadow-sm hover:shadow-md',
        gradient: 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl',
      },
      size: {
        default: 'h-10 py-2 px-4',
        sm: 'h-8 px-3 text-xs',
        lg: 'h-12 px-6 text-base',
        xl: 'h-14 px-8 text-lg',
        icon: 'h-10 w-10',
        'icon-sm': 'h-8 w-8',
        'icon-lg': 'h-12 w-12',
      },
      rounded: {
        default: 'rounded-lg',
        sm: 'rounded-md',
        lg: 'rounded-xl',
        full: 'rounded-full',
        none: 'rounded-none',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      rounded: 'default',
    },
  }
);

export interface OptimizedButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  tooltip?: string;
  badge?: string | number;
  animate?: boolean;
}

const OptimizedButtonComponent = forwardRef<HTMLButtonElement, OptimizedButtonProps>(
  ({ 
    className, 
    variant, 
    size, 
    rounded,
    loading = false,
    leftIcon,
    rightIcon,
    fullWidth = false,
    tooltip,
    badge,
    animate = true,
    children,
    disabled,
    ...props 
  }, ref) => {
    const isDisabled = disabled || loading;

    // Memoize the button classes to prevent unnecessary recalculations
    const buttonClasses = useMemo(() => cn(
      buttonVariants({ variant, size, rounded }),
      fullWidth && 'w-full',
      !animate && 'active:scale-100',
      className
    ), [variant, size, rounded, fullWidth, animate, className]);

    // Memoize the content to prevent unnecessary re-renders
    const buttonContent = useMemo(() => (
      <>
        {loading && <LoadingSpinner size="sm" className="mr-2" />}
        {!loading && leftIcon && <span className="mr-2 flex-shrink-0">{leftIcon}</span>}
        <span className="flex-1 truncate">{children}</span>
        {!loading && rightIcon && <span className="ml-2 flex-shrink-0">{rightIcon}</span>}
        {badge && (
          <span className="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full">
            {badge}
          </span>
        )}
      </>
    ), [loading, leftIcon, rightIcon, children, badge]);

    const buttonElement = (
      <button
        className={buttonClasses}
        ref={ref}
        disabled={isDisabled}
        {...props}
      >
        {buttonContent}
      </button>
    );

    // Add tooltip wrapper if tooltip is provided
    if (tooltip) {
      return (
        <div className="relative group">
          {buttonElement}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
            {tooltip}
          </div>
        </div>
      );
    }

    return buttonElement;
  }
);

OptimizedButtonComponent.displayName = 'OptimizedButton';

export const OptimizedButton = memo(OptimizedButtonComponent);

// Button group component for related actions
export interface ButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'outline';
}

const ButtonGroupComponent: React.FC<ButtonGroupProps> = ({
  children,
  className,
  orientation = 'horizontal',
  size = 'default',
  variant = 'default',
}) => {
  const groupClasses = cn(
    'inline-flex',
    orientation === 'horizontal' ? 'flex-row' : 'flex-col',
    variant === 'outline' && 'border border-gray-300 rounded-lg overflow-hidden',
    className
  );

  return (
    <div className={groupClasses} role="group">
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child)) {
          const isFirst = index === 0;
          const isLast = index === React.Children.count(children) - 1;
          
          return React.cloneElement(child, {
            className: cn(
              child.props.className,
              orientation === 'horizontal' && !isFirst && '-ml-px',
              orientation === 'vertical' && !isFirst && '-mt-px',
              orientation === 'horizontal' && !isFirst && !isLast && 'rounded-none',
              orientation === 'horizontal' && isFirst && 'rounded-r-none',
              orientation === 'horizontal' && isLast && 'rounded-l-none',
              orientation === 'vertical' && !isFirst && !isLast && 'rounded-none',
              orientation === 'vertical' && isFirst && 'rounded-b-none',
              orientation === 'vertical' && isLast && 'rounded-t-none'
            ),
            size: child.props.size || size,
          });
        }
        return child;
      })}
    </div>
  );
};

export const ButtonGroup = memo(ButtonGroupComponent);

// Floating Action Button component
export interface FABProps extends OptimizedButtonProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  offset?: string;
}

const FABComponent = forwardRef<HTMLButtonElement, FABProps>(
  ({ position = 'bottom-right', offset = '1rem', className, ...props }, ref) => {
    const positionClasses = {
      'bottom-right': 'bottom-4 right-4',
      'bottom-left': 'bottom-4 left-4',
      'top-right': 'top-4 right-4',
      'top-left': 'top-4 left-4',
    };

    return (
      <div className={cn('fixed z-50', positionClasses[position])} style={{ margin: offset }}>
        <OptimizedButton
          ref={ref}
          className={cn('shadow-lg hover:shadow-xl', className)}
          rounded="full"
          size="icon-lg"
          {...props}
        />
      </div>
    );
  }
);

FABComponent.displayName = 'FAB';

export const FAB = memo(FABComponent);

export { buttonVariants };
