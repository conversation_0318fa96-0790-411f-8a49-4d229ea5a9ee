import React from 'react';
import { 
  LayoutDashboard, 
  Send, 
  Users, 
  Server, 
  Bookmark, 
  Settings as SettingsIcon 
} from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { ViewType } from '../../types';

interface NavButtonProps {
  currentView: ViewType;
  targetView: ViewType;
  icon: React.ComponentType<{ size?: number }>;
  label: string;
}

const NavButton: React.FC<NavButtonProps> = ({ 
  currentView, 
  targetView, 
  icon: Icon, 
  label 
}) => {
  const { setView } = useApp();
  
  const isActive = currentView === targetView;
  
  return (
    <button 
      onClick={() => setView(targetView)} 
      className={`flex-1 md:flex-initial flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
        isActive 
          ? 'bg-white text-blue-700 shadow-sm' 
          : 'text-gray-500 hover:bg-gray-200 hover:text-gray-700'
      }`}
    >
      <Icon size={18} /> 
      <span>{label}</span>
    </button>
  );
};

export const Header: React.FC = () => {
  const { state, setView } = useApp();
  const { currentView } = state;

  return (
    <header className="flex flex-col md:flex-row items-center justify-between mb-6 gap-4">
      <h1 className="text-3xl font-bold text-gray-900 font-heading text-shadow">
        Disparador em Massa
      </h1>
      
      <nav className="w-full md:w-auto">
        <div className="flex items-center justify-center gap-1 p-1 bg-gray-100 rounded-xl flex-wrap shadow-sm">
          <NavButton 
            currentView={currentView} 
            targetView="dashboard" 
            icon={LayoutDashboard} 
            label="Dashboard" 
          />
          <NavButton 
            currentView={currentView} 
            targetView="main" 
            icon={Send} 
            label="Disparador" 
          />
          <NavButton 
            currentView={currentView} 
            targetView="contacts" 
            icon={Users} 
            label="Contatos" 
          />
          <NavButton 
            currentView={currentView} 
            targetView="groups" 
            icon={Users} 
            label="Grupos" 
          />
          <NavButton 
            currentView={currentView} 
            targetView="instances" 
            icon={Server} 
            label="Instâncias" 
          />
          <NavButton 
            currentView={currentView} 
            targetView="templates" 
            icon={Bookmark} 
            label="Templates" 
          />
          
          <button 
            onClick={() => setView('settings')} 
            title="Configurações" 
            className={`p-2.5 rounded-lg transition-all duration-200 ${
              currentView === 'settings' 
                ? 'bg-white text-blue-600 shadow-sm' 
                : 'text-gray-500 hover:bg-gray-200 hover:text-gray-700'
            }`}
          >
            <SettingsIcon size={18} />
          </button>
        </div>
      </nav>
    </header>
  );
};
