# WhatsApp Sender - Environment Variables

# Application
NEXT_PUBLIC_APP_NAME="WhatsApp Sender Optimized"
NEXT_PUBLIC_APP_VERSION="2.0.0"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# API Configuration
NEXT_PUBLIC_API_BASE_URL="https://your-whatsapp-api.com"
NEXT_PUBLIC_API_KEY="your-api-key-here"
NEXT_PUBLIC_API_TIMEOUT=30000

# WhatsApp API Endpoints (Evolution API / Baileys)
# Exemplo: https://api.evolutionapi.com
# Documentação: https://doc.evolutionapi.com

# Analytics & Monitoring
NEXT_PUBLIC_ANALYTICS_ENABLED=true
NEXT_PUBLIC_ERROR_TRACKING_ENABLED=true
NEXT_PUBLIC_PERFORMANCE_MONITORING=true

# Cache Configuration
NEXT_PUBLIC_CACHE_TTL=300000
NEXT_PUBLIC_CACHE_MAX_SIZE=100

# Feature Flags
NEXT_PUBLIC_ENABLE_WEB_WORKERS=true
NEXT_PUBLIC_ENABLE_VIRTUAL_SCROLLING=true
NEXT_PUBLIC_ENABLE_OFFLINE_MODE=true
NEXT_PUBLIC_ENABLE_PWA=true

# Development
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_SHOW_PERFORMANCE_METRICS=false

# AI Configuration (Optional)
NEXT_PUBLIC_OPENAI_API_KEY=""
NEXT_PUBLIC_GEMINI_API_KEY=""

# Webhook Configuration (Optional)
WEBHOOK_SECRET=""
WEBHOOK_URL=""

# Database (Optional - for future use)
DATABASE_URL=""
REDIS_URL=""

# Security
NEXT_PUBLIC_CSP_ENABLED=true
NEXT_PUBLIC_RATE_LIMIT_ENABLED=true
