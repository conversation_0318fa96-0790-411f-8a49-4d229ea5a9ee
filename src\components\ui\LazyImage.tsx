import React, { useState, useRef, useEffect } from 'react';
import { useIntersectionObserver } from '../../hooks/usePerformance';

interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  placeholder?: string;
  fallback?: string;
  className?: string;
  containerClassName?: string;
  blur?: boolean;
}

export const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  placeholder,
  fallback,
  className = '',
  containerClassName = '',
  blur = true,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [imageSrc, setImageSrc] = useState(placeholder || '');
  
  const { targetRef, hasIntersected } = useIntersectionObserver({
    threshold: 0.1,
    rootMargin: '50px',
  });

  useEffect(() => {
    if (hasIntersected && !isLoaded && !hasError) {
      const img = new Image();
      
      img.onload = () => {
        setImageSrc(src);
        setIsLoaded(true);
      };
      
      img.onerror = () => {
        setHasError(true);
        if (fallback) {
          setImageSrc(fallback);
        }
      };
      
      img.src = src;
    }
  }, [hasIntersected, src, fallback, isLoaded, hasError]);

  const imageClasses = `
    transition-all duration-300 ease-in-out
    ${isLoaded ? 'opacity-100' : 'opacity-0'}
    ${blur && !isLoaded ? 'blur-sm' : ''}
    ${className}
  `;

  const placeholderClasses = `
    absolute inset-0 bg-gray-200 animate-pulse
    transition-opacity duration-300
    ${isLoaded ? 'opacity-0' : 'opacity-100'}
  `;

  return (
    <div 
      ref={targetRef}
      className={`relative overflow-hidden ${containerClassName}`}
    >
      {/* Placeholder */}
      {!hasError && (
        <div className={placeholderClasses} />
      )}
      
      {/* Actual image */}
      {(hasIntersected || isLoaded) && !hasError && (
        <img
          src={imageSrc}
          alt={alt}
          className={imageClasses}
          onLoad={() => setIsLoaded(true)}
          onError={() => {
            setHasError(true);
            if (fallback) {
              setImageSrc(fallback);
            }
          }}
          {...props}
        />
      )}
      
      {/* Error state */}
      {hasError && !fallback && (
        <div className="absolute inset-0 bg-gray-200 flex items-center justify-center">
          <div className="text-gray-400 text-center">
            <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
            <span className="text-xs">Imagem não encontrada</span>
          </div>
        </div>
      )}
    </div>
  );
};
