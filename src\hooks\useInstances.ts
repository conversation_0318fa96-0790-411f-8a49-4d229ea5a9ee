import { useCallback } from 'react';
import { useApp } from '../contexts/AppContext';
import { useToast } from '../components/providers/ToastProvider';
import { Instance } from '../types';

export const useInstances = () => {
  const { state, updateInstances, setLoading } = useApp();
  const { addNotification } = useToast();
  const { settings } = state;

  const fetchInstances = useCallback(async () => {
    if (!settings.baseUrl || !settings.apiKey) {
      return;
    }

    setLoading(true);
    try {
      const fullUrl = `${settings.baseUrl.replace(/\/$/, '')}/instance/fetchInstances`;
      const response = await fetch(fullUrl, {
        headers: { 'apikey': settings.apiKey }
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Erro ao buscar instâncias');
      }

      let instances: Instance[] = [];
      
      if (Array.isArray(data) && data.length > 0 && data[0].instances) {
        instances = data[0].instances;
      } else if (Array.isArray(data)) {
        instances = data;
      } else {
        console.error("Formato de resposta inesperado:", data);
        instances = [];
      }

      updateInstances(instances);
    } catch (error) {
      console.error(error);
      addNotification("Erro ao buscar instâncias da API.", "error");
      updateInstances([]);
    } finally {
      setLoading(false);
    }
  }, [settings, updateInstances, setLoading, addNotification]);

  const createInstance = useCallback(async (instanceName: string) => {
    if (!settings.baseUrl || !settings.apiKey) {
      addNotification("Configure a API primeiro.", "error");
      return null;
    }

    try {
      const fullUrl = `${settings.baseUrl.replace(/\/$/, '')}/instance/create`;
      const response = await fetch(fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': settings.apiKey
        },
        body: JSON.stringify({
          instanceName,
          qrcode: true
        })
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || data.error || 'Erro ao criar instância');
      }

      addNotification(`Instância "${instanceName}" criada com sucesso!`, "success");
      
      // Refresh instances list
      setTimeout(() => fetchInstances(), 1000);
      
      return data;
    } catch (error: any) {
      addNotification(error.message, "error");
      return null;
    }
  }, [settings, addNotification, fetchInstances]);

  const getQRCode = useCallback(async (instanceName: string) => {
    if (!settings.baseUrl || !settings.apiKey) {
      addNotification("Configure a API primeiro.", "error");
      return null;
    }

    try {
      const fullUrl = `${settings.baseUrl.replace(/\/$/, '')}/instance/connect/${instanceName}`;
      const response = await fetch(fullUrl, {
        headers: { 'apikey': settings.apiKey }
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || data.error || 'Erro ao obter QR Code');
      }

      let qrString = '';
      if (typeof data === 'string') {
        qrString = data;
      } else if (data.base64 && typeof data.base64 === 'string') {
        qrString = data.base64;
      } else if (data.qrcode && typeof data.qrcode === 'string') {
        qrString = data.qrcode;
      }

      if (qrString) {
        const qrCode = qrString.startsWith('data:image') ? qrString : `data:image/png;base64,${qrString}`;
        addNotification('QR Code obtido! Aponte a câmera do WhatsApp para ler.', "info");
        return qrCode;
      } else {
        throw new Error(`Não foi possível obter o QR Code. Resposta: ${JSON.stringify(data)}`);
      }
    } catch (error: any) {
      addNotification(error.message, "error");
      return null;
    }
  }, [settings, addNotification]);

  const checkInstanceStatus = useCallback(async (instanceName: string) => {
    if (!settings.baseUrl || !settings.apiKey) {
      addNotification("Configure a API primeiro.", "error");
      return null;
    }

    try {
      const fullUrl = `${settings.baseUrl.replace(/\/$/, '')}/instance/connectionState/${instanceName}`;
      const response = await fetch(fullUrl, {
        headers: { 'apikey': settings.apiKey }
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || data.error || 'Erro ao verificar status');
      }

      addNotification(`Status da instância "${instanceName}" verificado.`, "success");
      return data;
    } catch (error: any) {
      addNotification(error.message, "error");
      return null;
    }
  }, [settings, addNotification]);

  return {
    instances: state.instances,
    loading: state.loading,
    fetchInstances,
    createInstance,
    getQRCode,
    checkInstanceStatus,
  };
};
