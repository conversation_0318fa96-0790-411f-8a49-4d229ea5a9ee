import React, { useEffect, memo } from 'react';
import { useApp } from '../../contexts/AppContext';
import { useToast } from '../providers/ToastProvider';
import { Header } from './Header';
import { ViewRenderer } from './ViewRenderer';
import { useInstances } from '../../hooks/useInstances';
import { usePerformanceMonitor } from '../../hooks/usePerformance';
import { observePerformance } from '../../utils/performance';

const MainLayoutComponent: React.FC = () => {
  const { state, setView } = useApp();
  const { addNotification } = useToast();
  const { fetchInstances } = useInstances();

  // Performance monitoring in development
  usePerformanceMonitor('MainLayout');

  useEffect(() => {
    // Start performance monitoring
    const stopObserving = observePerformance();

    return stopObserving;
  }, []);

  useEffect(() => {
    const { settings } = state;

    // Check if settings are configured
    if (!settings.baseUrl || !settings.apiKey) {
      setView('settings');
      addNotification("Bem-vindo! Por favor, configure sua API para começar.", "info");
    } else {
      // Fetch instances if settings are configured
      fetchInstances();
    }
  }, [state.settings, setView, addNotification, fetchInstances]);

  return (
    <div className="bg-gray-50 min-h-screen w-full flex p-4 sm:p-6 lg:p-8 font-sans text-gray-900">
      <div className="w-full max-w-7xl mx-auto">
        <Header />
        <main className="animate-fade-in">
          <ViewRenderer />
        </main>
      </div>
    </div>
  );
};

export const MainLayout = memo(MainLayoutComponent);
