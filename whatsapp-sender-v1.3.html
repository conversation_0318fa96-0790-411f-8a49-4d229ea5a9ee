<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Sender v1.3 - Sistema Profissional</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/papaparse@5.4.1/papaparse.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', sans-serif; }
        
        /* Animações suaves */
        .animate-fade-in { animation: fadeIn 0.5s ease-out; }
        .animate-slide-up { animation: slideUp 0.3s ease-out; }
        .animate-pulse-slow { animation: pulse 2s ease-in-out infinite; }
        .loading { animation: spin 1s linear infinite; }
        
        /* Gradientes modernos */
        .gradient-primary { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
        .gradient-success { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
        .gradient-warning { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
        .gradient-danger { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }
        .gradient-info { background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); }
        
        /* Glass effect */
        .glass-effect { 
            backdrop-filter: blur(10px); 
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        /* Sombras */
        .shadow-soft { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
        .shadow-medium { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
        .shadow-large { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
        
        /* Hover effects */
        .hover-lift { transition: all 0.3s ease; }
        .hover-lift:hover { transform: translateY(-2px); box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15); }
        
        @keyframes fadeIn { 
            from { opacity: 0; transform: translateY(20px); } 
            to { opacity: 1; transform: translateY(0); } 
        }
        @keyframes slideUp { 
            from { opacity: 0; transform: translateY(10px); } 
            to { opacity: 1; transform: translateY(0); } 
        }
        @keyframes spin { 
            from { transform: rotate(0deg); } 
            to { transform: rotate(360deg); } 
        }
        
        /* Scrollbar personalizada */
        ::-webkit-scrollbar { width: 6px; }
        ::-webkit-scrollbar-track { background: #f1f5f9; border-radius: 3px; }
        ::-webkit-scrollbar-thumb { background: #cbd5e1; border-radius: 3px; }
        ::-webkit-scrollbar-thumb:hover { background: #94a3b8; }
        
        /* Typography */
        .text-gradient { 
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useCallback, useRef } = React;

        // Sistema de armazenamento
        const Storage = {
            save: (key, data) => {
                try {
                    localStorage.setItem(`whatsapp_v13_${key}`, JSON.stringify(data));
                    console.log(`💾 Dados salvos: ${key}`, data);
                    return true;
                } catch (e) {
                    console.error(`❌ Erro ao salvar ${key}:`, e);
                    return false;
                }
            },
            load: (key, defaultValue = null) => {
                try {
                    const saved = localStorage.getItem(`whatsapp_v13_${key}`);
                    const data = saved ? JSON.parse(saved) : defaultValue;
                    console.log(`📂 Dados carregados: ${key}`, data);
                    return data;
                } catch (e) {
                    console.error(`❌ Erro ao carregar ${key}:`, e);
                    return defaultValue;
                }
            },
            clear: (key) => {
                try {
                    localStorage.removeItem(`whatsapp_v13_${key}`);
                    console.log(`🗑️ Dados removidos: ${key}`);
                    return true;
                } catch (e) {
                    console.error(`❌ Erro ao remover ${key}:`, e);
                    return false;
                }
            }
        };

        // Sistema de notificações
        const useNotification = () => {
            const [notifications, setNotifications] = useState([]);

            const addNotification = useCallback((message, type = 'info', duration = 5000) => {
                const id = Date.now() + Math.random();
                const notification = { id, message, type, duration };
                
                console.log(`🔔 Notificação: [${type.toUpperCase()}] ${message}`);
                setNotifications(prev => [...prev, notification]);
                
                setTimeout(() => {
                    setNotifications(prev => prev.filter(n => n.id !== id));
                }, duration);
            }, []);

            const removeNotification = useCallback((id) => {
                setNotifications(prev => prev.filter(n => n.id !== id));
            }, []);

            return { notifications, addNotification, removeNotification };
        };

        // Utilitário para converter arquivo para base64
        const toBase64 = (file) => new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result.split(',')[1]);
            reader.onerror = error => reject(error);
        });

        // Sistema de API
        const useAPI = () => {
            const [loading, setLoading] = useState(false);
            const { addNotification } = useNotification();

            const makeRequest = useCallback(async (endpoint, options = {}) => {
                const settings = Storage.load('settings', {});
                
                if (!settings.baseUrl || !settings.apiKey) {
                    throw new Error('Configure a URL Base e API Key nas Configurações');
                }

                const url = `${settings.baseUrl.replace(/\/$/, '')}/${endpoint.replace(/^\//, '')}`;
                
                console.log(`🌐 Fazendo requisição para: ${url}`);
                
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': settings.apiKey,
                        ...options.headers,
                    },
                });

                console.log(`📡 Resposta: ${response.status} ${response.statusText}`);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error(`❌ Erro da API:`, errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                console.log(`📦 Dados recebidos:`, data);
                return data;
            }, []);

            // Buscar contatos da instância
            const fetchContactsFromInstance = useCallback(async (instanceName) => {
                setLoading(true);
                try {
                    console.log(`👥 Buscando contatos da instância: ${instanceName}`);
                    const data = await makeRequest(`/chat/findContacts/${instanceName}`, {
                        method: 'POST'
                    });
                    
                    console.log('📱 Contatos brutos da instância:', data);
                    
                    if (!Array.isArray(data)) {
                        throw new Error('Resposta da API não é uma lista válida');
                    }

                    const contacts = data.map(contact => ({
                        id: contact.id || `inst_${Date.now()}_${Math.random()}`,
                        name: contact.pushName || contact.name || 'Sem nome',
                        phone: contact.remoteJid ? contact.remoteJid.split('@')[0] : '',
                        tags: ['sincronizado'],
                        source: 'instance',
                        profilePictureUrl: contact.profilePictureUrl || null,
                        createdAt: new Date().toISOString()
                    })).filter(c => c.phone && c.phone.length >= 10);

                    console.log('✅ Contatos processados da instância:', contacts);
                    return contacts;
                } catch (error) {
                    console.error('❌ Erro ao buscar contatos da instância:', error);
                    addNotification('Erro ao buscar contatos da instância: ' + error.message, 'error');
                    throw error;
                } finally {
                    setLoading(false);
                }
            }, [makeRequest, addNotification]);

            // Enviar texto
            const sendText = useCallback(async (instanceName, number, text) => {
                try {
                    console.log(`📤 Enviando texto para ${number} via ${instanceName}:`, text);
                    
                    const result = await makeRequest(`/message/sendText/${instanceName}`, {
                        method: 'POST',
                        body: JSON.stringify({
                            number: number,
                            text: text,
                            options: { delay: 1000 }
                        })
                    });
                    
                    console.log('✅ Texto enviado com sucesso:', result);
                    return result;
                } catch (error) {
                    console.error(`❌ Erro ao enviar texto para ${number}:`, error);
                    throw error;
                }
            }, [makeRequest]);

            // Enviar mídia
            const sendMedia = useCallback(async (instanceName, number, mediaData, caption = '') => {
                try {
                    console.log(`📎 Enviando mídia para ${number} via ${instanceName}:`, {
                        type: mediaData.type,
                        fileName: mediaData.fileName
                    });
                    
                    const result = await makeRequest(`/message/sendMedia/${instanceName}`, {
                        method: 'POST',
                        body: JSON.stringify({
                            number: number,
                            mediatype: mediaData.mediatype,
                            mimetype: mediaData.mimetype,
                            caption: caption,
                            media: mediaData.base64,
                            fileName: mediaData.fileName,
                            options: { delay: 1000 }
                        })
                    });
                    
                    console.log('✅ Mídia enviada com sucesso:', result);
                    return result;
                } catch (error) {
                    console.error(`❌ Erro ao enviar mídia para ${number}:`, error);
                    throw error;
                }
            }, [makeRequest]);

            // Enviar áudio
            const sendAudio = useCallback(async (instanceName, number, audioBase64) => {
                try {
                    console.log(`🎵 Enviando áudio para ${number} via ${instanceName}`);
                    
                    const result = await makeRequest(`/message/sendWhatsAppAudio/${instanceName}`, {
                        method: 'POST',
                        body: JSON.stringify({
                            number: number,
                            audio: audioBase64,
                            options: { delay: 1000 }
                        })
                    });
                    
                    console.log('✅ Áudio enviado com sucesso:', result);
                    return result;
                } catch (error) {
                    console.error(`❌ Erro ao enviar áudio para ${number}:`, error);
                    throw error;
                }
            }, [makeRequest]);

            // Outras funções da API
            const fetchInstances = useCallback(async () => {
                setLoading(true);
                try {
                    const data = await makeRequest('/instance/fetchInstances');
                    console.log('🖥️ Instâncias brutas da API:', data);

                    let instances = [];

                    if (Array.isArray(data)) {
                        instances = data;
                    } else if (data.instances && Array.isArray(data.instances)) {
                        instances = data.instances;
                    } else if (data.data && Array.isArray(data.data)) {
                        instances = data.data;
                    }

                    const normalizedInstances = instances.map(inst => {
                        const instanceData = inst.instance || inst;

                        return {
                            id: instanceData.instanceName || instanceData.name || inst.instanceName || Math.random().toString(),
                            name: instanceData.instanceName || instanceData.name || inst.instanceName || 'Sem nome',
                            status: instanceData.state || inst.state || inst.connectionStatus || 'close',
                            profileName: instanceData.profileName || inst.profileName || null,
                            profilePictureUrl: instanceData.profilePictureUrl || inst.profilePictureUrl || null
                        };
                    });

                    console.log('✅ Instâncias normalizadas:', normalizedInstances);
                    Storage.save('instances', normalizedInstances);
                    return normalizedInstances;
                } catch (error) {
                    console.error('❌ Erro ao buscar instâncias:', error);
                    addNotification('Erro ao buscar instâncias: ' + error.message, 'error');
                    throw error;
                } finally {
                    setLoading(false);
                }
            }, [makeRequest, addNotification]);

            const createInstance = useCallback(async (name) => {
                setLoading(true);
                try {
                    const result = await makeRequest('/instance/create', {
                        method: 'POST',
                        body: JSON.stringify({
                            instanceName: name,
                            qrcode: true,
                            integration: 'WHATSAPP-BAILEYS'
                        })
                    });

                    addNotification(`Instância "${name}" criada com sucesso!`, 'success');
                    return result;
                } catch (error) {
                    console.error('❌ Erro ao criar instância:', error);
                    addNotification('Erro ao criar instância: ' + error.message, 'error');
                    throw error;
                } finally {
                    setLoading(false);
                }
            }, [makeRequest, addNotification]);

            const getQRCode = useCallback(async (instanceName) => {
                setLoading(true);
                try {
                    const result = await makeRequest(`/instance/connect/${instanceName}`);
                    console.log('📱 QR Code recebido:', result);

                    let qrString = '';
                    if (typeof result === 'string') {
                        qrString = result;
                    } else if (result.base64) {
                        qrString = result.base64;
                    } else if (result.qrcode) {
                        qrString = result.qrcode;
                    } else if (result.code) {
                        qrString = result.code;
                    }

                    if (qrString) {
                        const qrImage = qrString.startsWith('data:image') ? qrString : `data:image/png;base64,${qrString}`;
                        return qrImage;
                    } else {
                        throw new Error('QR Code não encontrado na resposta');
                    }
                } catch (error) {
                    console.error('❌ Erro ao obter QR Code:', error);
                    addNotification('Erro ao obter QR Code: ' + error.message, 'error');
                    throw error;
                } finally {
                    setLoading(false);
                }
            }, [makeRequest, addNotification]);

            const getInstanceStatus = useCallback(async (instanceName) => {
                try {
                    const result = await makeRequest(`/instance/connectionState/${instanceName}`);
                    console.log('📊 Status da instância:', result);
                    return result;
                } catch (error) {
                    console.error('❌ Erro ao verificar status:', error);
                    addNotification('Erro ao verificar status: ' + error.message, 'error');
                    throw error;
                }
            }, [makeRequest, addNotification]);

            const testConnection = useCallback(async () => {
                setLoading(true);
                try {
                    const instances = await fetchInstances();
                    addNotification('Conexão testada com sucesso!', 'success');
                    return { success: true, data: instances };
                } catch (error) {
                    addNotification('Erro na conexão: ' + error.message, 'error');
                    return { success: false, error: error.message };
                }
            }, [fetchInstances, addNotification]);

            return {
                loading,
                fetchInstances,
                createInstance,
                getQRCode,
                getInstanceStatus,
                testConnection,
                fetchContactsFromInstance,
                sendText,
                sendMedia,
                sendAudio
            };
        };

        // Componentes UI Modernos
        const Card = ({ children, className = '', hover = true }) => (
            <div className={`
                bg-white rounded-xl shadow-soft border border-gray-100 p-6 transition-all duration-300
                ${hover ? 'hover-lift' : ''}
                ${className}
            `}>
                {children}
            </div>
        );

        const Button = ({
            children,
            onClick,
            disabled,
            variant = 'primary',
            size = 'md',
            className = '',
            loading = false,
            icon = null,
            fullWidth = false
        }) => {
            const variants = {
                primary: 'gradient-primary text-white shadow-medium hover:shadow-large',
                secondary: 'bg-gray-100 text-gray-700 hover:bg-gray-200 shadow-soft hover:shadow-medium',
                success: 'gradient-success text-white shadow-medium hover:shadow-large',
                danger: 'gradient-danger text-white shadow-medium hover:shadow-large',
                warning: 'gradient-warning text-white shadow-medium hover:shadow-large',
                info: 'gradient-info text-white shadow-medium hover:shadow-large',
                outline: 'border-2 border-gray-300 hover:border-gray-400 bg-white hover:bg-gray-50 text-gray-700'
            };

            const sizes = {
                sm: 'px-3 py-1.5 text-sm',
                md: 'px-4 py-2 text-sm',
                lg: 'px-6 py-3 text-base',
                xl: 'px-8 py-4 text-lg'
            };

            return (
                <button
                    onClick={onClick}
                    disabled={disabled || loading}
                    className={`
                        inline-flex items-center justify-center gap-2 rounded-lg font-medium
                        transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50
                        transform hover:scale-105 active:scale-95
                        ${variants[variant]} ${sizes[size]} ${fullWidth ? 'w-full' : ''} ${className}
                    `}
                >
                    {loading && <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full loading"></div>}
                    {icon && !loading && <span>{icon}</span>}
                    {children}
                </button>
            );
        };

        const Badge = ({ children, variant = 'default', size = 'md' }) => {
            const variants = {
                default: 'bg-blue-100 text-blue-800',
                success: 'bg-green-100 text-green-800',
                error: 'bg-red-100 text-red-800',
                warning: 'bg-yellow-100 text-yellow-800',
                info: 'bg-cyan-100 text-cyan-800'
            };

            const sizes = {
                sm: 'px-2 py-0.5 text-xs',
                md: 'px-2.5 py-0.5 text-xs',
                lg: 'px-3 py-1 text-sm'
            };

            return (
                <span className={`inline-flex items-center rounded-full font-medium ${variants[variant]} ${sizes[size]}`}>
                    {children}
                </span>
            );
        };

        const Input = ({ label, error, icon, className = '', ...props }) => (
            <div className="space-y-2">
                {label && (
                    <label className="block text-sm font-medium text-gray-700">
                        {label}
                    </label>
                )}
                <div className="relative">
                    {icon && (
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span className="text-gray-400">{icon}</span>
                        </div>
                    )}
                    <input
                        {...props}
                        className={`
                            w-full px-3 py-2 border border-gray-300 rounded-lg transition-all duration-200
                            focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                            ${icon ? 'pl-10' : ''}
                            ${error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}
                            ${props.disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white hover:border-gray-400'}
                            ${className}
                        `}
                    />
                </div>
                {error && <p className="text-sm text-red-600">{error}</p>}
            </div>
        );

        const Textarea = ({ label, error, className = '', ...props }) => (
            <div className="space-y-2">
                {label && (
                    <label className="block text-sm font-medium text-gray-700">
                        {label}
                    </label>
                )}
                <textarea
                    {...props}
                    className={`
                        w-full px-3 py-2 border border-gray-300 rounded-lg transition-all duration-200
                        focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none
                        ${error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}
                        ${props.disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white hover:border-gray-400'}
                        ${className}
                    `}
                />
                {error && <p className="text-sm text-red-600">{error}</p>}
            </div>
        );

        const Select = ({ label, error, children, className = '', ...props }) => (
            <div className="space-y-2">
                {label && (
                    <label className="block text-sm font-medium text-gray-700">
                        {label}
                    </label>
                )}
                <select
                    {...props}
                    className={`
                        w-full px-3 py-2 border border-gray-300 rounded-lg transition-all duration-200
                        focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                        ${error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}
                        ${props.disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white hover:border-gray-400'}
                        ${className}
                    `}
                >
                    {children}
                </select>
                {error && <p className="text-sm text-red-600">{error}</p>}
            </div>
        );

        // Componente de Notificações
        const NotificationContainer = ({ notifications, removeNotification }) => (
            <div className="fixed top-4 right-4 z-50 space-y-3 max-w-sm">
                {notifications.map(notification => (
                    <div
                        key={notification.id}
                        className={`
                            p-4 rounded-lg shadow-large border-l-4 animate-slide-up
                            ${notification.type === 'success' ? 'bg-green-50 border-green-500 text-green-800' : ''}
                            ${notification.type === 'error' ? 'bg-red-50 border-red-500 text-red-800' : ''}
                            ${notification.type === 'warning' ? 'bg-yellow-50 border-yellow-500 text-yellow-800' : ''}
                            ${notification.type === 'info' ? 'bg-blue-50 border-blue-500 text-blue-800' : ''}
                        `}
                    >
                        <div className="flex items-start gap-3">
                            <span className="text-lg flex-shrink-0">
                                {notification.type === 'success' && '✅'}
                                {notification.type === 'error' && '❌'}
                                {notification.type === 'warning' && '⚠️'}
                                {notification.type === 'info' && 'ℹ️'}
                            </span>
                            <div className="flex-1">
                                <p className="text-sm font-medium leading-relaxed">
                                    {notification.message}
                                </p>
                            </div>
                            <button
                                onClick={() => removeNotification(notification.id)}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                            >
                                <span className="text-lg">×</span>
                            </button>
                        </div>
                    </div>
                ))}
            </div>
        );

        // Modal
        const Modal = ({ isOpen, onClose, title, children, size = 'md' }) => {
            if (!isOpen) return null;

            const sizes = {
                sm: 'max-w-md',
                md: 'max-w-lg',
                lg: 'max-w-2xl',
                xl: 'max-w-4xl'
            };

            return (
                <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                    <div className={`bg-white rounded-xl shadow-large w-full ${sizes[size]} max-h-[90vh] overflow-hidden animate-slide-up`}>
                        <div className="flex justify-between items-center p-6 border-b border-gray-100">
                            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
                            <button
                                onClick={onClose}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                            >
                                <span className="text-2xl">×</span>
                            </button>
                        </div>
                        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                            {children}
                        </div>
                    </div>
                </div>
            );
        };

        // Dashboard Aprimorado
        const Dashboard = () => {
            const contacts = Storage.load('contacts', []);
            const instances = Storage.load('instances', []);
            const settings = Storage.load('settings', {});
            const stats = Storage.load('stats', { total: 0, success: 0, errors: 0, lastRun: null });

            const connectedInstances = instances.filter(i => i.status === 'open').length;
            const isConfigured = settings.baseUrl && settings.apiKey;
            const isReady = isConfigured && connectedInstances > 0 && contacts.length > 0;

            // Calcular estatísticas
            const successRate = stats.total > 0 ? Math.round((stats.success / stats.total) * 100) : 0;
            const totalContacts = contacts.length;
            const csvContacts = contacts.filter(c => c.source === 'csv').length;
            const instanceContacts = contacts.filter(c => c.source === 'instance').length;
            const manualContacts = contacts.filter(c => c.source === 'manual').length;

            return (
                <div className="space-y-6">
                    {/* Header do Dashboard */}
                    <Card className="text-center bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                        <div className="flex items-center justify-center gap-4 mb-4">
                            <div className="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center">
                                <span className="text-white text-2xl">📊</span>
                            </div>
                            <div>
                                <h1 className="text-2xl font-bold text-gradient">WhatsApp Sender v1.3</h1>
                                <p className="text-gray-600">Sistema Profissional de Disparos</p>
                            </div>
                        </div>
                        <Badge variant={isReady ? 'success' : 'warning'} size="lg">
                            {isReady ? '🚀 Sistema Pronto' : '⚙️ Configuração Necessária'}
                        </Badge>
                    </Card>

                    {/* Cards de Estatísticas Principais */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <Card className="text-center bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                            <div className="text-3xl mb-2">🖥️</div>
                            <h3 className="font-semibold text-gray-800">Instâncias</h3>
                            <p className="text-2xl font-bold text-blue-600">{connectedInstances}/{instances.length}</p>
                            <p className="text-sm text-gray-600">Conectadas</p>
                        </Card>

                        <Card className="text-center bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                            <div className="text-3xl mb-2">👥</div>
                            <h3 className="font-semibold text-gray-800">Contatos</h3>
                            <p className="text-2xl font-bold text-green-600">{totalContacts}</p>
                            <p className="text-sm text-gray-600">Cadastrados</p>
                        </Card>

                        <Card className="text-center bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                            <div className="text-3xl mb-2">⚙️</div>
                            <h3 className="font-semibold text-gray-800">API</h3>
                            <p className="text-2xl font-bold text-purple-600">
                                {isConfigured ? '✅' : '❌'}
                            </p>
                            <p className="text-sm text-gray-600">
                                {isConfigured ? 'Configurada' : 'Pendente'}
                            </p>
                        </Card>

                        <Card className="text-center bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
                            <div className="text-3xl mb-2">📈</div>
                            <h3 className="font-semibold text-gray-800">Taxa de Sucesso</h3>
                            <p className="text-2xl font-bold text-yellow-600">{successRate}%</p>
                            <p className="text-sm text-gray-600">Última campanha</p>
                        </Card>
                    </div>

                    {/* Distribuição de Contatos */}
                    {totalContacts > 0 && (
                        <Card>
                            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
                                <span>📊</span> Distribuição de Contatos
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="text-center p-4 bg-blue-50 rounded-lg">
                                    <div className="text-2xl mb-2">📁</div>
                                    <p className="font-semibold text-blue-800">{csvContacts}</p>
                                    <p className="text-sm text-blue-600">Importados CSV</p>
                                </div>
                                <div className="text-center p-4 bg-green-50 rounded-lg">
                                    <div className="text-2xl mb-2">📱</div>
                                    <p className="font-semibold text-green-800">{instanceContacts}</p>
                                    <p className="text-sm text-green-600">Sincronizados</p>
                                </div>
                                <div className="text-center p-4 bg-gray-50 rounded-lg">
                                    <div className="text-2xl mb-2">✋</div>
                                    <p className="font-semibold text-gray-800">{manualContacts}</p>
                                    <p className="text-sm text-gray-600">Manuais</p>
                                </div>
                            </div>
                        </Card>
                    )}

                    {/* Últimas Estatísticas */}
                    {stats.lastRun && (
                        <Card>
                            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
                                <span>📈</span> Última Campanha
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div className="text-center p-4 bg-blue-50 rounded-lg">
                                    <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
                                    <p className="text-sm text-gray-600">Total Enviado</p>
                                </div>
                                <div className="text-center p-4 bg-green-50 rounded-lg">
                                    <p className="text-2xl font-bold text-green-600">{stats.success}</p>
                                    <p className="text-sm text-gray-600">Sucessos</p>
                                </div>
                                <div className="text-center p-4 bg-red-50 rounded-lg">
                                    <p className="text-2xl font-bold text-red-600">{stats.errors}</p>
                                    <p className="text-sm text-gray-600">Erros</p>
                                </div>
                                <div className="text-center p-4 bg-gray-50 rounded-lg">
                                    <p className="text-xs text-gray-600">Executada em</p>
                                    <p className="text-sm font-medium text-gray-800">{stats.lastRun}</p>
                                </div>
                            </div>
                        </Card>
                    )}

                    {/* Status do Sistema */}
                    <Card>
                        <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
                            <span>🔧</span> Status do Sistema
                        </h2>
                        <div className="space-y-3">
                            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center gap-3">
                                    <span className="text-lg">⚙️</span>
                                    <span className="font-medium">Configuração da API</span>
                                </div>
                                <Badge variant={isConfigured ? 'success' : 'error'}>
                                    {isConfigured ? 'Configurada' : 'Pendente'}
                                </Badge>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center gap-3">
                                    <span className="text-lg">🖥️</span>
                                    <span className="font-medium">Instâncias Conectadas</span>
                                </div>
                                <Badge variant={connectedInstances > 0 ? 'success' : 'error'}>
                                    {connectedInstances > 0 ? `${connectedInstances} Conectada(s)` : 'Nenhuma'}
                                </Badge>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center gap-3">
                                    <span className="text-lg">👥</span>
                                    <span className="font-medium">Base de Contatos</span>
                                </div>
                                <Badge variant={totalContacts > 0 ? 'success' : 'warning'}>
                                    {totalContacts > 0 ? `${totalContacts} Contato(s)` : 'Vazia'}
                                </Badge>
                            </div>
                        </div>
                    </Card>

                    {/* Primeiros Passos ou Ações Rápidas */}
                    {!isReady ? (
                        <Card className="bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
                            <h2 className="text-lg font-semibold mb-4 text-yellow-800 flex items-center gap-2">
                                <span>🎯</span> Primeiros Passos
                            </h2>
                            <div className="space-y-3 text-sm">
                                <div className="flex items-center gap-3">
                                    {isConfigured ? '✅' : '1️⃣'}
                                    <span className={`${isConfigured ? 'line-through text-gray-500' : 'font-medium text-yellow-800'}`}>
                                        Configure sua Evolution API na aba "Configurações"
                                    </span>
                                </div>
                                <div className="flex items-center gap-3">
                                    {connectedInstances > 0 ? '✅' : '2️⃣'}
                                    <span className={`${connectedInstances > 0 ? 'line-through text-gray-500' : 'font-medium text-yellow-800'}`}>
                                        Configure instâncias na aba "Instâncias"
                                    </span>
                                </div>
                                <div className="flex items-center gap-3">
                                    {totalContacts > 0 ? '✅' : '3️⃣'}
                                    <span className={`${totalContacts > 0 ? 'line-through text-gray-500' : 'font-medium text-yellow-800'}`}>
                                        Adicione contatos na aba "Contatos"
                                    </span>
                                </div>
                                <div className="flex items-center gap-3">
                                    {isReady ? '✅' : '4️⃣'}
                                    <span className={`${isReady ? 'line-through text-gray-500' : 'font-medium text-yellow-800'}`}>
                                        Execute campanhas na aba "Disparador"
                                    </span>
                                </div>
                            </div>
                        </Card>
                    ) : (
                        <Card>
                            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
                                <span>🚀</span> Sistema Pronto - Ações Rápidas
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <Button
                                    onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'campaign' }))}
                                    variant="primary"
                                    size="lg"
                                    fullWidth
                                    icon="📱"
                                >
                                    Nova Campanha
                                </Button>
                                <Button
                                    onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'contacts' }))}
                                    variant="success"
                                    size="lg"
                                    fullWidth
                                    icon="👥"
                                >
                                    Gerenciar Contatos
                                </Button>
                                <Button
                                    onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'instances' }))}
                                    variant="info"
                                    size="lg"
                                    fullWidth
                                    icon="🖥️"
                                >
                                    Ver Instâncias
                                </Button>
                            </div>
                        </Card>
                    )}

                    {/* Dicas e Informações */}
                    <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                        <h2 className="text-lg font-semibold mb-4 text-blue-800 flex items-center gap-2">
                            <span>💡</span> Dicas do Sistema v1.3
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
                            <div className="space-y-2">
                                <p className="font-medium">📱 Sincronização de Contatos:</p>
                                <p>Importe contatos diretamente da sua instância WhatsApp conectada</p>
                            </div>
                            <div className="space-y-2">
                                <p className="font-medium">📎 Multimídia Completa:</p>
                                <p>Envie imagens, vídeos, áudios e documentos em sequência</p>
                            </div>
                            <div className="space-y-2">
                                <p className="font-medium">📊 Campos Dinâmicos:</p>
                                <p>Use qualquer coluna do CSV como variável {{campo}}</p>
                            </div>
                            <div className="space-y-2">
                                <p className="font-medium">🔄 Delays Inteligentes:</p>
                                <p>Configure delays entre 2-5 segundos para evitar bloqueios</p>
                            </div>
                        </div>
                    </Card>
                </div>
            );
        };

        // Componente de Configurações
        const Settings = () => {
            const [settings, setSettings] = useState(() => Storage.load('settings', {
                baseUrl: '',
                apiKey: '',
                minDelay: '2',
                maxDelay: '5'
            }));
            const [errors, setErrors] = useState({});
            const { addNotification } = useNotification();

            const validateSettings = () => {
                const newErrors = {};

                if (!settings.baseUrl) {
                    newErrors.baseUrl = 'URL Base é obrigatória';
                } else if (!settings.baseUrl.startsWith('http')) {
                    newErrors.baseUrl = 'URL deve começar com http:// ou https://';
                }

                if (!settings.apiKey) {
                    newErrors.apiKey = 'API Key é obrigatória';
                } else if (settings.apiKey.length < 8) {
                    newErrors.apiKey = 'API Key deve ter pelo menos 8 caracteres';
                }

                if (parseFloat(settings.minDelay) < 1) {
                    newErrors.minDelay = 'Delay mínimo deve ser pelo menos 1 segundo';
                }

                if (parseFloat(settings.maxDelay) < parseFloat(settings.minDelay)) {
                    newErrors.maxDelay = 'Delay máximo deve ser maior que o mínimo';
                }

                setErrors(newErrors);
                return Object.keys(newErrors).length === 0;
            };

            const handleSave = () => {
                if (!validateSettings()) {
                    addNotification('Corrija os erros antes de salvar', 'error');
                    return;
                }

                if (Storage.save('settings', settings)) {
                    addNotification('Configurações salvas com sucesso!', 'success');
                } else {
                    addNotification('Erro ao salvar configurações', 'error');
                }
            };

            const handleInputChange = (field, value) => {
                setSettings(prev => ({ ...prev, [field]: value }));
                if (errors[field]) {
                    setErrors(prev => ({ ...prev, [field]: '' }));
                }
            };

            return (
                <div className="space-y-6">
                    <Card>
                        <div className="flex items-center gap-4 mb-6">
                            <div className="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center">
                                <span className="text-white text-xl">⚙️</span>
                            </div>
                            <div>
                                <h2 className="text-xl font-bold text-gradient">Configurações da API</h2>
                                <p className="text-gray-600">Configure sua Evolution API para começar</p>
                            </div>
                        </div>

                        <div className="space-y-6">
                            <Input
                                label="URL Base da API *"
                                type="url"
                                value={settings.baseUrl}
                                onChange={(e) => handleInputChange('baseUrl', e.target.value)}
                                placeholder="http://localhost:8080"
                                error={errors.baseUrl}
                                icon="🌐"
                            />

                            <Input
                                label="API Key *"
                                type="password"
                                value={settings.apiKey}
                                onChange={(e) => handleInputChange('apiKey', e.target.value)}
                                placeholder="Sua chave da Evolution API"
                                error={errors.apiKey}
                                icon="🔑"
                            />

                            <div className="grid grid-cols-2 gap-4">
                                <Input
                                    label="Delay Mínimo (segundos)"
                                    type="number"
                                    min="1"
                                    step="0.1"
                                    value={settings.minDelay}
                                    onChange={(e) => handleInputChange('minDelay', e.target.value)}
                                    error={errors.minDelay}
                                    icon="⏱️"
                                />
                                <Input
                                    label="Delay Máximo (segundos)"
                                    type="number"
                                    min="1"
                                    step="0.1"
                                    value={settings.maxDelay}
                                    onChange={(e) => handleInputChange('maxDelay', e.target.value)}
                                    error={errors.maxDelay}
                                    icon="⏰"
                                />
                            </div>

                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <h4 className="font-semibold text-yellow-800 mb-2 flex items-center gap-2">
                                    <span>💡</span> Dicas Importantes
                                </h4>
                                <ul className="text-sm text-yellow-700 space-y-1">
                                    <li>• Certifique-se que sua Evolution API está rodando</li>
                                    <li>• Configure CORS_ORIGIN=* no .env da Evolution API</li>
                                    <li>• Use delays de 2-5 segundos para evitar bloqueios</li>
                                    <li>• Teste a conexão após salvar as configurações</li>
                                </ul>
                            </div>

                            <Button
                                onClick={handleSave}
                                variant="primary"
                                size="lg"
                                fullWidth
                                icon="💾"
                            >
                                Salvar Configurações
                            </Button>
                        </div>
                    </Card>
                </div>
            );
        };

        // Componente de Contatos Simplificado
        const Contacts = () => {
            const [contacts, setContacts] = useState(() => Storage.load('contacts', []));
            const [newContact, setNewContact] = useState({ name: '', phone: '', tags: '' });
            const [searchTerm, setSearchTerm] = useState('');
            const [editingContact, setEditingContact] = useState(null);
            const [isImportModalOpen, setIsImportModalOpen] = useState(false);
            const [csvData, setCsvData] = useState([]);
            const [csvHeaders, setCsvHeaders] = useState([]);
            const [fieldMapping, setFieldMapping] = useState({});
            const [syncInstance, setSyncInstance] = useState('');
            const [isSyncing, setIsSyncing] = useState(false);
            const fileInputRef = useRef(null);

            const instances = Storage.load('instances', []);
            const connectedInstances = instances.filter(i => i.status === 'open');
            const { addNotification } = useNotification();
            const { fetchContactsFromInstance } = useAPI();

            const filteredContacts = contacts.filter(contact =>
                contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                contact.phone.includes(searchTerm) ||
                (contact.tags && contact.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))
            );

            useEffect(() => {
                if (connectedInstances.length > 0 && !syncInstance) {
                    setSyncInstance(connectedInstances[0].name);
                }
            }, [connectedInstances, syncInstance]);

            const validateContact = (contact) => {
                if (!contact.name?.trim()) {
                    addNotification('Nome é obrigatório', 'error');
                    return false;
                }
                if (!contact.phone?.trim()) {
                    addNotification('Telefone é obrigatório', 'error');
                    return false;
                }
                const cleanPhone = contact.phone.replace(/\D/g, '');
                if (cleanPhone.length < 10 || cleanPhone.length > 15) {
                    addNotification('Telefone deve ter entre 10 e 15 dígitos', 'error');
                    return false;
                }
                return true;
            };

            const addContact = () => {
                if (!validateContact(newContact)) return;

                const contact = {
                    id: Date.now().toString(),
                    name: newContact.name.trim(),
                    phone: newContact.phone.replace(/\D/g, ''),
                    tags: newContact.tags.split(',').map(t => t.trim()).filter(Boolean),
                    source: 'manual',
                    createdAt: new Date().toISOString()
                };

                const updatedContacts = [...contacts, contact];
                setContacts(updatedContacts);
                Storage.save('contacts', updatedContacts);
                setNewContact({ name: '', phone: '', tags: '' });
                addNotification('Contato adicionado com sucesso!', 'success');
            };

            const deleteContact = (id) => {
                if (confirm('Tem certeza que deseja excluir este contato?')) {
                    const updatedContacts = contacts.filter(c => c.id !== id);
                    setContacts(updatedContacts);
                    Storage.save('contacts', updatedContacts);
                    addNotification('Contato excluído', 'success');
                }
            };

            // Sincronizar contatos da instância
            const handleSyncContacts = async () => {
                if (!syncInstance) {
                    addNotification('Selecione uma instância para sincronizar', 'error');
                    return;
                }

                setIsSyncing(true);
                addNotification(`Sincronizando contatos da instância "${syncInstance}"...`, 'info');

                try {
                    const instanceContacts = await fetchContactsFromInstance(syncInstance);

                    if (instanceContacts.length === 0) {
                        addNotification('Nenhum contato encontrado na instância', 'warning');
                        return;
                    }

                    const existingPhones = new Set(contacts.map(c => c.phone));
                    let newContactsCount = 0;

                    const newContacts = instanceContacts.filter(contact => {
                        if (existingPhones.has(contact.phone)) {
                            return false;
                        }
                        existingPhones.add(contact.phone);
                        newContactsCount++;
                        return true;
                    });

                    if (newContacts.length > 0) {
                        const updatedContacts = [...contacts, ...newContacts];
                        setContacts(updatedContacts);
                        Storage.save('contacts', updatedContacts);

                        // Atualizar campos disponíveis
                        const allFields = new Set();
                        updatedContacts.forEach(contact => {
                            Object.keys(contact).forEach(key => {
                                if (!['id', 'createdAt', 'updatedAt', 'source'].includes(key)) {
                                    allFields.add(key);
                                }
                            });
                        });
                        Storage.save('contactFields', Array.from(allFields));
                    }

                    addNotification(
                        `${newContactsCount} novos contatos sincronizados da instância!`,
                        'success'
                    );
                } catch (error) {
                    console.error('Erro ao sincronizar contatos:', error);
                } finally {
                    setIsSyncing(false);
                }
            };

            return (
                <div className="space-y-6">
                    <Card>
                        <div className="flex items-center justify-between mb-6">
                            <div className="flex items-center gap-4">
                                <div className="w-12 h-12 gradient-success rounded-xl flex items-center justify-center">
                                    <span className="text-white text-xl">👥</span>
                                </div>
                                <div>
                                    <h2 className="text-xl font-bold text-gradient">Gerenciar Contatos</h2>
                                    <p className="text-gray-600">{contacts.length} contatos cadastrados</p>
                                </div>
                            </div>
                            <div className="flex gap-3">
                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept=".csv"
                                    onChange={() => {}} // Implementar depois
                                    className="hidden"
                                />
                                <Button
                                    onClick={() => fileInputRef.current?.click()}
                                    variant="success"
                                    icon="📁"
                                >
                                    Importar CSV
                                </Button>
                            </div>
                        </div>

                        {/* Sincronização de Instância */}
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                            <h3 className="font-semibold mb-3 text-blue-800 flex items-center gap-2">
                                <span>📱</span> Sincronizar da Instância WhatsApp
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 items-end">
                                <div className="md:col-span-2">
                                    <Select
                                        label="Selecionar Instância Conectada"
                                        value={syncInstance}
                                        onChange={(e) => setSyncInstance(e.target.value)}
                                        disabled={connectedInstances.length === 0}
                                    >
                                        {connectedInstances.length === 0 ? (
                                            <option>Nenhuma instância conectada</option>
                                        ) : (
                                            connectedInstances.map(inst => (
                                                <option key={inst.id} value={inst.name}>
                                                    {inst.name} ({inst.profileName || 'Conectado'})
                                                </option>
                                            ))
                                        )}
                                    </Select>
                                </div>
                                <Button
                                    onClick={handleSyncContacts}
                                    disabled={isSyncing || connectedInstances.length === 0}
                                    loading={isSyncing}
                                    variant="primary"
                                    fullWidth
                                    icon="🔄"
                                >
                                    {isSyncing ? 'Sincronizando...' : 'Sincronizar'}
                                </Button>
                            </div>
                        </div>

                        {/* Adicionar Contato */}
                        <div className="bg-gray-50 rounded-lg p-4 mb-6">
                            <h3 className="font-semibold mb-3">➕ Adicionar Novo Contato</h3>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                                <Input
                                    placeholder="Nome completo"
                                    value={newContact.name}
                                    onChange={(e) => setNewContact({...newContact, name: e.target.value})}
                                />
                                <Input
                                    placeholder="5511999998888"
                                    value={newContact.phone}
                                    onChange={(e) => setNewContact({...newContact, phone: e.target.value})}
                                />
                                <Input
                                    placeholder="cliente, vip, sp"
                                    value={newContact.tags}
                                    onChange={(e) => setNewContact({...newContact, tags: e.target.value})}
                                />
                                <Button
                                    onClick={addContact}
                                    variant="primary"
                                    fullWidth
                                    icon="➕"
                                >
                                    Adicionar
                                </Button>
                            </div>
                        </div>

                        {/* Busca */}
                        <div className="mb-6">
                            <Input
                                placeholder="🔍 Buscar contatos por nome, telefone ou tag..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                icon="🔍"
                            />
                        </div>

                        {/* Lista de Contatos */}
                        <div className="bg-white rounded-lg border overflow-hidden">
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="text-left p-4 font-semibold text-gray-700">Nome</th>
                                            <th className="text-left p-4 font-semibold text-gray-700">Telefone</th>
                                            <th className="text-left p-4 font-semibold text-gray-700">Tags</th>
                                            <th className="text-left p-4 font-semibold text-gray-700">Origem</th>
                                            <th className="text-center p-4 font-semibold text-gray-700">Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {filteredContacts.map((contact, index) => (
                                            <tr key={contact.id} className={`border-b hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`}>
                                                <td className="p-4">
                                                    <div className="font-medium text-gray-900">{contact.name}</div>
                                                </td>
                                                <td className="p-4 font-mono text-sm text-gray-600">{contact.phone}</td>
                                                <td className="p-4">
                                                    <div className="flex gap-1 flex-wrap">
                                                        {contact.tags?.map(tag => (
                                                            <Badge key={tag} variant="default" size="sm">{tag}</Badge>
                                                        ))}
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <Badge
                                                        variant={contact.source === 'instance' ? 'info' : contact.source === 'csv' ? 'warning' : 'default'}
                                                        size="sm"
                                                    >
                                                        {contact.source === 'instance' ? '📱 WhatsApp' :
                                                         contact.source === 'csv' ? '📁 CSV' : '✋ Manual'}
                                                    </Badge>
                                                </td>
                                                <td className="p-4 text-center">
                                                    <Button
                                                        onClick={() => deleteContact(contact.id)}
                                                        variant="danger"
                                                        size="sm"
                                                        icon="🗑️"
                                                    >
                                                        Excluir
                                                    </Button>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>

                                {filteredContacts.length === 0 && (
                                    <div className="text-center py-12 text-gray-500">
                                        <div className="text-6xl mb-4">📭</div>
                                        <h3 className="text-lg font-semibold mb-2">
                                            {searchTerm ? 'Nenhum contato encontrado' : 'Nenhum contato cadastrado'}
                                        </h3>
                                        <p className="text-sm">
                                            {searchTerm ? 'Tente uma busca diferente' : 'Adicione contatos manualmente ou sincronize da instância'}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </Card>
                </div>
            );
        };

        // Componente Principal da Aplicação
        const App = () => {
            const [currentView, setCurrentView] = useState('dashboard');
            const { notifications, addNotification, removeNotification } = useNotification();

            const views = {
                dashboard: { name: 'Dashboard', icon: '📊', component: Dashboard },
                contacts: { name: 'Contatos', icon: '👥', component: Contacts },
                settings: { name: 'Configurações', icon: '⚙️', component: Settings }
            };

            const CurrentComponent = views[currentView]?.component || Dashboard;

            // Listener para navegação via eventos
            useEffect(() => {
                const handleNavigate = (event) => {
                    setCurrentView(event.detail);
                };
                window.addEventListener('navigate', handleNavigate);
                return () => window.removeEventListener('navigate', handleNavigate);
            }, []);

            return (
                <div className="min-h-screen bg-gray-50">
                    {/* Header Limpo */}
                    <header className="bg-white shadow-soft border-b border-gray-200 sticky top-0 z-40">
                        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="flex justify-between items-center h-16">
                                <div className="flex items-center gap-4">
                                    <div className="w-10 h-10 gradient-primary rounded-lg flex items-center justify-center">
                                        <span className="text-white font-bold text-lg">WS</span>
                                    </div>
                                    <div>
                                        <h1 className="text-lg font-bold text-gradient">
                                            WhatsApp Sender v1.3
                                        </h1>
                                        <p className="text-xs text-gray-500">Sistema Profissional</p>
                                    </div>
                                </div>
                                <div className="flex gap-2">
                                    <Badge variant="success" size="sm">
                                        ✅ Funcional
                                    </Badge>
                                    <Badge variant="info" size="sm">
                                        📱 Multimídia
                                    </Badge>
                                </div>
                            </div>
                        </div>
                    </header>

                    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                        {/* Navigation Limpa */}
                        <div className="mb-6">
                            <nav className="flex gap-2 bg-white p-2 rounded-lg shadow-soft border border-gray-200">
                                {Object.entries(views).map(([key, view]) => (
                                    <button
                                        key={key}
                                        onClick={() => setCurrentView(key)}
                                        className={`
                                            px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2
                                            ${currentView === key
                                                ? 'gradient-primary text-white shadow-medium'
                                                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                                            }
                                        `}
                                    >
                                        <span>{view.icon}</span>
                                        <span className="hidden sm:inline">{view.name}</span>
                                    </button>
                                ))}
                            </nav>
                        </div>

                        {/* Content */}
                        <main className="animate-fade-in">
                            <CurrentComponent />
                        </main>
                    </div>

                    {/* Footer Limpo */}
                    <footer className="bg-white border-t border-gray-200 mt-12">
                        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
                                <div className="text-center md:text-left">
                                    <p className="text-sm font-semibold text-gray-800">
                                        WhatsApp Sender v1.3
                                    </p>
                                    <p className="text-xs text-gray-600">
                                        Sistema profissional com sincronização e multimídia
                                    </p>
                                </div>
                                <div className="flex items-center gap-4 text-xs text-gray-500">
                                    <div className="flex items-center gap-1">
                                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                        <span>React 18</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                        <span>Tailwind CSS</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                        <span>Evolution API</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </footer>

                    {/* Notificações */}
                    <NotificationContainer
                        notifications={notifications}
                        removeNotification={removeNotification}
                    />
                </div>
            );
        };

        // Renderizar a aplicação
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
