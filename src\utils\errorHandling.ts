// Error handling utilities

export class AppError extends Error {
  public readonly code: string;
  public readonly statusCode?: number;
  public readonly isOperational: boolean;

  constructor(
    message: string,
    code: string = 'UNKNOWN_ERROR',
    statusCode?: number,
    isOperational: boolean = true
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, AppError);
  }
}

export class NetworkError extends AppError {
  constructor(message: string = 'Erro de conexão com o servidor') {
    super(message, 'NETWORK_ERROR', 0);
  }
}

export class ValidationError extends AppError {
  constructor(message: string = 'Dados inválidos') {
    super(message, 'VALIDATION_ERROR', 400);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Erro de autenticação') {
    super(message, 'AUTHENTICATION_ERROR', 401);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Acesso negado') {
    super(message, 'AUTHORIZATION_ERROR', 403);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Recurso não encontrado') {
    super(message, 'NOT_FOUND_ERROR', 404);
  }
}

// Error handler for API calls
export const handleApiError = (error: any): AppError => {
  // Network errors
  if (!error.response) {
    return new NetworkError('Não foi possível conectar ao servidor. Verifique sua conexão.');
  }

  const { status, data } = error.response;
  const message = data?.message || data?.error || 'Erro desconhecido';

  switch (status) {
    case 400:
      return new ValidationError(message);
    case 401:
      return new AuthenticationError(message);
    case 403:
      return new AuthorizationError(message);
    case 404:
      return new NotFoundError(message);
    case 500:
      return new AppError('Erro interno do servidor', 'SERVER_ERROR', 500);
    default:
      return new AppError(message, 'API_ERROR', status);
  }
};

// Safe async function wrapper
export const safeAsync = async <T>(
  asyncFn: () => Promise<T>,
  fallbackValue?: T
): Promise<{ data?: T; error?: AppError }> => {
  try {
    const data = await asyncFn();
    return { data };
  } catch (error) {
    console.error('Error in safeAsync:', error);
    
    if (error instanceof AppError) {
      return { error };
    }
    
    // Convert unknown errors to AppError
    const appError = new AppError(
      error instanceof Error ? error.message : 'Erro desconhecido',
      'UNKNOWN_ERROR'
    );
    
    return { error: appError, data: fallbackValue };
  }
};

// Retry mechanism for failed operations
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (attempt === maxRetries) {
        throw lastError;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError!;
};

// Error logging utility
export const logError = (error: Error, context?: Record<string, any>) => {
  const errorLog = {
    message: error.message,
    stack: error.stack,
    name: error.name,
    timestamp: new Date().toISOString(),
    context,
  };

  console.error('Error logged:', errorLog);

  // In production, send to external logging service
  if (process.env.NODE_ENV === 'production') {
    // sendToLoggingService(errorLog);
  }
};

// User-friendly error messages
export const getErrorMessage = (error: any): string => {
  if (error instanceof AppError) {
    return error.message;
  }

  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === 'string') {
    return error;
  }

  return 'Ocorreu um erro inesperado. Tente novamente.';
};

// Validation helpers
export const validateRequired = (value: any, fieldName: string): void => {
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    throw new ValidationError(`${fieldName} é obrigatório`);
  }
};

export const validateEmail = (email: string): void => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new ValidationError('Email inválido');
  }
};

export const validatePhone = (phone: string): void => {
  const phoneRegex = /^\d{10,15}$/;
  if (!phoneRegex.test(phone.replace(/\D/g, ''))) {
    throw new ValidationError('Telefone inválido');
  }
};
