import { useCallback, useRef } from 'react';
import { useAppStore } from '@/stores/appStore';
import { useAdvancedApi } from './useAdvancedApi';
import type { CampaignConfig, Contact, Group, Message } from '@/types';

interface CampaignProgress {
  config: CampaignConfig;
  success: number;
  errors: string[];
  remainingTargets: (Contact | Group)[];
}

// Hook for managing campaign execution
export const useCampaign = () => {
  const campaignState = useAppStore((state) => state.campaignState);
  const setCampaignState = useAppStore((state) => state.setCampaignState);
  const resetCampaign = useAppStore((state) => state.resetCampaign);
  const updateStats = useAppStore((state) => state.updateStats);
  const { makeRequest } = useAdvancedApi();
  
  const abortControllerRef = useRef<AbortController | null>(null);

  // Convert file to base64
  const toBase64 = useCallback((file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result.split(',')[1]);
      };
      reader.onerror = reject;
    });
  }, []);

  // Send individual message
  const sendMessage = useCallback(async (
    target: Contact | Group,
    message: Message,
    instance: string,
    sendMode: 'contacts' | 'groups',
    mentionEveryone: boolean
  ) => {
    const recipient = sendMode === 'groups' ? target.id : (target as Contact).phone;
    
    // Personalize message text
    const personalizedText = message.text.replace(
      /\{\{([\w\d_]+)\}\}/g,
      (_, key) => (target as any)[key.trim()] || `{{${key}}}`
    );

    if (!personalizedText && !message.mediaFile) return;

    const intraMessageDelay = Math.floor(Math.random() * (5000 - 2000 + 1)) + 2000;
    const options = sendMode === 'groups' 
      ? { delay: intraMessageDelay, mentions: { everyone: mentionEveryone } }
      : { delay: intraMessageDelay };

    let endpoint: string;
    let payload: any;

    if (message.mediaFile && message.mediaFile.file) {
      const mediaBase64 = await toBase64(message.mediaFile.file);
      
      if (message.mediaFile.type === 'audio') {
        endpoint = `/message/sendWhatsAppAudio/${instance}`;
        payload = {
          number: recipient,
          audio: mediaBase64,
          options,
        };
      } else {
        endpoint = `/message/sendMedia/${instance}`;
        payload = {
          number: recipient,
          mediatype: message.mediaFile.type === 'application' ? 'document' : message.mediaFile.type,
          mimetype: message.mediaFile.file.type,
          caption: personalizedText,
          media: mediaBase64,
          fileName: message.mediaFile.file.name,
          options,
        };
      }
    } else {
      endpoint = `/message/sendText/${instance}`;
      payload = {
        number: recipient,
        text: personalizedText,
        options,
      };
    }

    const response = await makeRequest(endpoint, {
      method: 'POST',
      body: JSON.stringify(payload),
    });

    if (!response.success) {
      throw new Error(response.error?.message || 'Failed to send message');
    }

    return response.data;
  }, [makeRequest, toBase64]);

  // Execute campaign
  const runCampaign = useCallback(async (campaignProgress: CampaignProgress) => {
    const { config, success: initialSuccess, errors: initialErrors, remainingTargets } = campaignProgress;
    const { messages, instance, settings, sendMode, mentionEveryone } = config;
    
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;

    setCampaignState({ isSending: true, errors: initialErrors });

    let localErrors = [...initialErrors];
    let localSuccess = initialSuccess;
    const totalInitialTargets = config.targets.length;

    try {
      for (let i = 0; i < remainingTargets.length; i++) {
        if (signal.aborted) {
          // Save progress for resuming later
          const currentProgress = {
            ...campaignProgress,
            success: localSuccess,
            errors: localErrors,
            remainingTargets: remainingTargets.slice(i)
          };
          localStorage.setItem('whatsappSenderCampaignProgress', JSON.stringify(currentProgress));
          
          setCampaignState({ isSending: false, errors: localErrors });
          return;
        }

        const target = remainingTargets[i];
        const recipient = sendMode === 'groups' ? target.id : (target as Contact).phone;
        
        const processedCount = totalInitialTargets - remainingTargets.length + i + 1;
        setCampaignState({
          current: processedCount,
          message: `Processando ${target.name || recipient}...`,
          currentTarget: target,
        });

        if (!recipient) {
          localErrors.push(`Destinatário inválido: ${target.name || `índice ${i}`}.`);
          continue;
        }

        try {
          // Send all messages for this target
          for (let j = 0; j < messages.length; j++) {
            if (signal.aborted) break;

            const messageItem = messages[j];
            await sendMessage(target, messageItem, instance, sendMode, mentionEveryone);
          }
          
          localSuccess++;
        } catch (error: any) {
          localErrors.push(`Destinatário ${target.name || recipient}: ${error.message}`);
        }

        // Inter-contact delay
        if (i < remainingTargets.length - 1) {
          const min = parseFloat(settings.minDelay || '2') * 1000;
          const max = parseFloat(settings.maxDelay || '5') * 1000;
          const interContactDelay = Math.floor(Math.random() * (max - min + 1)) + min;
          await new Promise(resolve => setTimeout(resolve, interContactDelay));
        }

        // Update progress
        const newProgress = {
          ...campaignProgress,
          success: localSuccess,
          errors: localErrors,
          remainingTargets: remainingTargets.slice(i + 1)
        };
        localStorage.setItem('whatsappSenderCampaignProgress', JSON.stringify(newProgress));

        setCampaignState({
          success: localSuccess,
          errors: localErrors,
          progress: (processedCount / totalInitialTargets) * 100,
        });
      }

      // Campaign completed
      const finalStats = {
        total: totalInitialTargets,
        success: localSuccess,
        errors: localErrors.length,
        lastRun: new Date().toLocaleString('pt-BR'),
      };
      
      updateStats(finalStats);
      localStorage.removeItem('whatsappSenderCampaignProgress');

      setCampaignState({
        isSending: false,
        progress: 100,
        message: 'Campanha concluída!',
        endTime: new Date(),
      });

    } catch (error: any) {
      setCampaignState({
        isSending: false,
        errors: [...localErrors, error.message],
        message: 'Erro na campanha',
      });
    } finally {
      abortControllerRef.current = null;
    }
  }, [setCampaignState, updateStats, sendMessage]);

  // Start new campaign
  const startCampaign = useCallback((campaignConfig: CampaignConfig) => {
    const progress: CampaignProgress = {
      config: campaignConfig,
      success: 0,
      errors: [],
      remainingTargets: campaignConfig.targets,
    };
    
    localStorage.setItem('whatsappSenderCampaignProgress', JSON.stringify(progress));
    
    setCampaignState({
      isSending: true,
      progress: 0,
      success: 0,
      errors: [],
      current: 0,
      total: campaignConfig.targets.length,
      message: 'Iniciando campanha...',
      config: campaignConfig,
      startTime: new Date(),
    });
    
    runCampaign(progress);
  }, [setCampaignState, runCampaign]);

  // Resume paused campaign
  const resumeCampaign = useCallback(() => {
    const savedProgress = localStorage.getItem('whatsappSenderCampaignProgress');
    if (savedProgress) {
      const progress: CampaignProgress = JSON.parse(savedProgress);
      runCampaign(progress);
    }
  }, [runCampaign]);

  // Cancel campaign
  const cancelCampaign = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    } else {
      localStorage.removeItem('whatsappSenderCampaignProgress');
      resetCampaign();
    }
  }, [resetCampaign]);

  // Pause campaign
  const pauseCampaign = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // Get campaign progress from localStorage
  const getSavedCampaignProgress = useCallback(() => {
    const savedProgress = localStorage.getItem('whatsappSenderCampaignProgress');
    return savedProgress ? JSON.parse(savedProgress) : null;
  }, []);

  // Calculate estimated time remaining
  const getEstimatedTimeRemaining = useCallback(() => {
    if (!campaignState.startTime || campaignState.current === 0) return null;
    
    const elapsed = Date.now() - campaignState.startTime.getTime();
    const avgTimePerTarget = elapsed / campaignState.current;
    const remaining = campaignState.total - campaignState.current;
    
    return remaining * avgTimePerTarget;
  }, [campaignState]);

  return {
    campaignState,
    startCampaign,
    resumeCampaign,
    cancelCampaign,
    pauseCampaign,
    getSavedCampaignProgress,
    getEstimatedTimeRemaining,
    isRunning: campaignState.isSending,
    progress: campaignState.progress,
    currentTarget: campaignState.currentTarget,
  };
};
