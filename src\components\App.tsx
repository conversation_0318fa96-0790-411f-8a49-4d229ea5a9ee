import React from 'react';
import { AppProvider } from '../contexts/AppContext';
import { MainLayout } from './layout/MainLayout';
import { ToastProvider } from './providers/ToastProvider';
import { ErrorBoundary } from './ErrorBoundary';
import '../styles/globals.css';

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <AppProvider>
        <ToastProvider>
          <ErrorBoundary>
            <MainLayout />
          </ErrorBoundary>
        </ToastProvider>
      </AppProvider>
    </ErrorBoundary>
  );
};

export default App;
