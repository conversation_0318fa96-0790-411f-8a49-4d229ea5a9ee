import React, { useState, memo } from 'react';
import { useAppStore } from '@/stores/appStore';
import { useApiTester, validateApiConfig } from '@/utils/apiTest';
import { OptimizedButton } from '@/components/ui/OptimizedButton';
import { Card } from '@/components/ui/Card';
import { 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  Play, 
  Settings,
  Wifi,
  WifiOff
} from 'lucide-react';

interface TestResult {
  success: boolean;
  message: string;
  details?: any;
  endpoint?: string;
}

const ApiTesterComponent: React.FC = () => {
  const settings = useAppStore(state => state.settings);
  const { testApi, quickTest } = useApiTester();
  
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [recommendations, setRecommendations] = useState<string[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'disconnected'>('unknown');

  // Validar configuração atual
  const configValidation = validateApiConfig({
    baseUrl: settings.baseUrl,
    apiKey: settings.apiKey
  });

  // Teste rápido de conexão
  const handleQuickTest = async () => {
    setIsLoading(true);
    try {
      const result = await quickTest({
        baseUrl: settings.baseUrl,
        apiKey: settings.apiKey
      });
      
      setConnectionStatus(result.success ? 'connected' : 'disconnected');
      setTestResults([result]);
      
      if (!result.success) {
        setRecommendations(['Verifique a URL da API e a chave de acesso']);
      } else {
        setRecommendations([]);
      }
    } catch (error) {
      setConnectionStatus('disconnected');
      setTestResults([{
        success: false,
        message: 'Erro ao testar conexão',
        details: error
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  // Teste completo da API
  const handleFullTest = async () => {
    setIsLoading(true);
    try {
      const diagnosis = await testApi({
        baseUrl: settings.baseUrl,
        apiKey: settings.apiKey
      });
      
      setTestResults(diagnosis.tests);
      setRecommendations(diagnosis.recommendations);
      
      const hasSuccessfulConnection = diagnosis.tests.some(t => 
        t.endpoint === '/instance/fetchInstances' && t.success
      );
      setConnectionStatus(hasSuccessfulConnection ? 'connected' : 'disconnected');
      
    } catch (error) {
      setConnectionStatus('disconnected');
      setTestResults([{
        success: false,
        message: 'Erro ao executar testes',
        details: error
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  // Renderizar status da conexão
  const renderConnectionStatus = () => {
    const statusConfig = {
      unknown: { icon: Settings, color: 'text-gray-500', bg: 'bg-gray-100', text: 'Não testado' },
      connected: { icon: Wifi, color: 'text-green-600', bg: 'bg-green-100', text: 'Conectado' },
      disconnected: { icon: WifiOff, color: 'text-red-600', bg: 'bg-red-100', text: 'Desconectado' }
    };

    const config = statusConfig[connectionStatus];
    const Icon = config.icon;

    return (
      <div className={`flex items-center gap-2 px-3 py-2 rounded-lg ${config.bg}`}>
        <Icon className={`h-4 w-4 ${config.color}`} />
        <span className={`text-sm font-medium ${config.color}`}>
          {config.text}
        </span>
      </div>
    );
  };

  // Renderizar resultado de teste
  const renderTestResult = (result: TestResult, index: number) => (
    <div key={index} className={`p-3 rounded-lg border ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
      <div className="flex items-start gap-3">
        {result.success ? (
          <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
        ) : (
          <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
        )}
        <div className="flex-1">
          <p className={`font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
            {result.message}
          </p>
          {result.endpoint && (
            <p className="text-xs text-gray-500 mt-1">
              Endpoint: {result.endpoint}
            </p>
          )}
          {result.details && typeof result.details === 'string' && (
            <details className="mt-2">
              <summary className="text-xs text-gray-600 cursor-pointer">
                Ver detalhes
              </summary>
              <pre className="text-xs text-gray-500 mt-1 p-2 bg-gray-100 rounded overflow-auto">
                {result.details}
              </pre>
            </details>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-800">Teste de API</h3>
          <p className="text-sm text-gray-600">Verifique a conectividade com sua API WhatsApp</p>
        </div>
        {renderConnectionStatus()}
      </div>

      {/* Validação da Configuração */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-700 mb-3">Configuração Atual</h4>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            {settings.baseUrl ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-600" />
            )}
            <span className="text-sm">
              URL Base: {settings.baseUrl || 'Não configurada'}
            </span>
          </div>
          <div className="flex items-center gap-2">
            {settings.apiKey ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-600" />
            )}
            <span className="text-sm">
              API Key: {settings.apiKey ? `Configurada (${settings.apiKey.length} caracteres)` : 'Não configurada'}
            </span>
          </div>
        </div>

        {!configValidation.valid && (
          <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm font-medium text-yellow-800 mb-1">Problemas de Configuração:</p>
            <ul className="text-sm text-yellow-700 list-disc list-inside">
              {configValidation.errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Botões de Teste */}
      <div className="flex gap-3 mb-6">
        <OptimizedButton
          onClick={handleQuickTest}
          disabled={isLoading || !configValidation.valid}
          loading={isLoading}
          variant="outline"
          leftIcon={<Wifi size={16} />}
        >
          Teste Rápido
        </OptimizedButton>
        
        <OptimizedButton
          onClick={handleFullTest}
          disabled={isLoading || !configValidation.valid}
          loading={isLoading}
          variant="default"
          leftIcon={<Play size={16} />}
        >
          Teste Completo
        </OptimizedButton>
      </div>

      {/* Resultados dos Testes */}
      {testResults.length > 0 && (
        <div className="mb-6">
          <h4 className="font-medium text-gray-700 mb-3">Resultados dos Testes</h4>
          <div className="space-y-3">
            {testResults.map(renderTestResult)}
          </div>
        </div>
      )}

      {/* Recomendações */}
      {recommendations.length > 0 && (
        <div className="mb-6">
          <h4 className="font-medium text-gray-700 mb-3">Recomendações</h4>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <ul className="text-sm text-blue-800 space-y-1">
              {recommendations.map((rec, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-blue-600 mt-0.5">•</span>
                  {rec}
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* Informações de Ajuda */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-700 mb-2">Como Configurar</h4>
        <div className="text-sm text-gray-600 space-y-1">
          <p>1. Configure a <strong>URL Base</strong> da sua API (ex: http://localhost:8080)</p>
          <p>2. Configure a <strong>API Key</strong> fornecida pela sua API</p>
          <p>3. Execute o <strong>Teste Rápido</strong> para verificar a conexão</p>
          <p>4. Se conectado, execute o <strong>Teste Completo</strong> para validar todas as funcionalidades</p>
        </div>
      </div>
    </Card>
  );
};

export const ApiTester = memo(ApiTesterComponent);
